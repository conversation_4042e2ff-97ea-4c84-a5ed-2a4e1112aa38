"use client"

import React, { useMemo, useState } from "react"

import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

interface ActionItem {
  id: number
  type: "review" | "comment" | "shared"
  title: string
  requester: string
  requesterRole: string
  date: string
  priority: "High" | "Medium" | "Low"
  status: "Pending" | "Completed"
  description?: string
}

const actionItems: ActionItem[] = [
  {
    id: 1,
    type: "review",
    title: "Q2 Profitability Analysis Review",
    requester: "<PERSON>",
    requesterRole: "Senior Analyst",
    date: "2025-05-10",
    priority: "High",
    status: "Pending",
    description: "Review of Q2 profitability metrics and forecasting models",
  },
  {
    id: 2,
    type: "comment",
    title: "Cost Reduction Strategy Feedback",
    requester: "<PERSON>",
    requesterRole: "Team Lead",
    date: "2025-05-09",
    priority: "Medium",
    status: "Pending",
    description: "Feedback requested on proposed cost reduction initiatives",
  },
  {
    id: 3,
    type: "shared",
    title: "Market Expansion Opportunity Analysis",
    requester: "<PERSON>",
    requesterRole: "Market Analyst",
    date: "2025-05-08",
    priority: "Medium",
    status: "Pending",
    description:
      "Analysis of potential market expansion opportunities in APAC region",
  },
  {
    id: 4,
    type: "review",
    title: "Supply Chain Efficiency Report",
    requester: "David Lee",
    requesterRole: "Operations Analyst",
    date: "2025-05-07",
    priority: "Low",
    status: "Pending",
    description: "Review of supply chain optimization recommendations",
  },
  {
    id: 5,
    type: "shared",
    title: "Competitor Pricing Strategy Analysis",
    requester: "Jennifer Taylor",
    requesterRole: "Competitive Analyst",
    date: "2025-05-06",
    priority: "High",
    status: "Pending",
    description: "Analysis of competitor pricing strategies in key markets",
  },
  {
    id: 6,
    type: "comment",
    title: "New Product Line Financial Projection",
    requester: "Robert Brown",
    requesterRole: "Product Manager",
    date: "2025-05-05",
    priority: "High",
    status: "Pending",
    description: "Financial projections for the new product line launch in Q3",
  },
  {
    id: 7,
    type: "review",
    title: "Annual Budget Allocation Review",
    requester: "Katherine Wang",
    requesterRole: "Finance Director",
    date: "2025-05-04",
    priority: "High",
    status: "Completed",
    description: "Review of annual budget allocations across departments",
  },
  {
    id: 8,
    type: "shared",
    title: "Customer Retention Analysis",
    requester: "Thomas Wilson",
    requesterRole: "Customer Success Lead",
    date: "2025-05-03",
    priority: "Medium",
    status: "Completed",
    description:
      "Analysis of customer retention metrics and improvement strategies",
  },
  {
    id: 9,
    type: "comment",
    title: "Quarterly Investor Presentation Draft",
    requester: "Alexandra Martinez",
    requesterRole: "Investor Relations",
    date: "2025-05-02",
    priority: "High",
    status: "Completed",
    description:
      "Draft of quarterly investor presentation for review and feedback",
  },
  {
    id: 10,
    type: "review",
    title: "Operational Risk Assessment",
    requester: "James Peterson",
    requesterRole: "Risk Manager",
    date: "2025-05-01",
    priority: "Medium",
    status: "Pending",
    description: "Assessment of operational risks in manufacturing facilities",
  },
  {
    id: 11,
    type: "shared",
    title: "Sales Performance Dashboard",
    requester: "Olivia Garcia",
    requesterRole: "Sales Analytics",
    date: "2025-04-30",
    priority: "Low",
    status: "Pending",
    description: "Interactive dashboard showing sales performance by region",
  },
  {
    id: 12,
    type: "comment",
    title: "Marketing Campaign ROI Analysis",
    requester: "Daniel Kim",
    requesterRole: "Marketing Director",
    date: "2025-04-29",
    priority: "Medium",
    status: "Completed",
    description: "Analysis of return on investment for Q1 marketing campaigns",
  },
]

const Actions = () => {
  const [filter, setFilter] = useState("all")
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10
  const [sortField, setSortField] = useState("date")
  const [sortDirection, setSortDirection] = useState("desc")
  const [hideCompleted, setHideCompleted] = useState(false)

  // Get pending actions count from context
  const pendingActionsCount = 9

  // Apply filters, search, and sorting with useMemo for performance
  const filteredSortedActions = useMemo(() => {
    // First filter by type
    let result =
      filter === "all"
        ? [...actionItems]
        : actionItems.filter((item) => item.type === filter)

    // Then filter by completion status if hideCompleted is true
    if (hideCompleted) {
      result = result.filter((item) => item.status !== "Completed")
    }

    // Then filter by search query
    if (searchQuery.trim() !== "") {
      const query = searchQuery.toLowerCase()
      result = result.filter(
        (item) =>
          item.title.toLowerCase().includes(query) ||
          item.requester.toLowerCase().includes(query) ||
          item.requesterRole.toLowerCase().includes(query) ||
          item.description?.toLowerCase().includes(query)
      )
    }

    // Then sort by the selected field and direction
    result.sort((a, b) => {
      let aValue: number = a[sortField as keyof ActionItem] as number
      let bValue: number = b[sortField as keyof ActionItem] as number

      // Convert dates to timestamps for comparison
      if (sortField === "date") {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Compare the values
      if (aValue < bValue) return sortDirection === "asc" ? -1 : 1
      if (aValue > bValue) return sortDirection === "asc" ? 1 : -1
      return 0
    })

    return result

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    actionItems,
    filter,
    hideCompleted,
    searchQuery,
    sortField,
    sortDirection,
  ])

  // Get priority color
  const getPriorityColor = (priority: "High" | "Medium" | "Low"): string => {
    switch (priority) {
      case "High":
        return "text-red-600"
      case "Medium":
        return "text-amber-600"
      case "Low":
        return "text-green-600"
      default:
        return "text-gray-600"
    }
  }

  // Get type label
  const getTypeLabel = (type: "review" | "comment" | "shared"): string => {
    switch (type) {
      case "review":
        return "Review Request"
      case "comment":
        return "Comment Request"
      case "shared":
        return "Shared Analysis"
      default:
        return "Other"
    }
  }

  // Get type icon - using professional, serious icons
  const getTypeIcon = (
    type: "review" | "comment" | "shared"
  ): React.ReactNode => {
    switch (type) {
      case "review":
        return (
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            ></path>
          </svg>
        )
      case "comment":
        return (
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
            ></path>
          </svg>
        )
      case "shared":
        return (
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2"
            ></path>
          </svg>
        )
      default:
        return (
          <svg
            className="h-5 w-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            ></path>
          </svg>
        )
    }
  }

  // Get status badge color
  const getStatusBadgeColor = (status: "Pending" | "Completed"): string => {
    switch (status) {
      case "Pending":
        return "bg-yellow-100 text-yellow-800"
      case "Completed":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  // Handle sort change
  const handleSort = (field: string) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("asc")
    }
  }

  // Pagination logic
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredSortedActions.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredSortedActions, currentPage, itemsPerPage])

  const totalPages = Math.ceil(filteredSortedActions.length / itemsPerPage)

  return (
    <>
      <Breadcrumb links={[{ label: "Actions" }]} />

      <UnderConstructionAlert />

      <main className="flex-grow overflow-auto">
        <div>
          <div className="mb-6 flex items-center justify-between">
            <div className="flex items-center">
              <h1 className="border-primary text-primary border-b pb-2 text-2xl font-semibold">
                Actions
              </h1>
              <span className="bg-primary ml-4 rounded-full px-3 py-1 text-sm font-semibold text-white">
                {pendingActionsCount} pending
              </span>
            </div>
          </div>

          {/* Search and filter controls */}
          <div className="mb-4 rounded-lg bg-white p-4 shadow-md">
            <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0 md:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg
                      className="h-5 w-5 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                      />
                    </svg>
                  </div>
                  <input
                    type="text"
                    className="focus:border-primary focus:ring-primary block w-full rounded-md border border-gray-300 bg-white py-2 pr-3 pl-10 leading-5 placeholder-gray-500 focus:outline-none sm:text-sm"
                    placeholder="Search actions..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <input
                    id="hide-completed"
                    name="hide-completed"
                    type="checkbox"
                    className="text-primary focus:ring-primary h-4 w-4 rounded border-gray-300"
                    checked={hideCompleted}
                    onChange={() => setHideCompleted(!hideCompleted)}
                  />
                  <label
                    htmlFor="hide-completed"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Hide completed
                  </label>
                </div>

                <div className="flex space-x-1">
                  <button
                    className={`rounded-md px-3 py-1.5 text-sm transition-colors ${filter === "all" ? "bg-primary text-white" : "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"}`}
                    onClick={() => setFilter("all")}
                  >
                    All
                  </button>
                  <button
                    className={`rounded-md px-3 py-1.5 text-sm transition-colors ${filter === "review" ? "bg-primary text-white" : "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"}`}
                    onClick={() => setFilter("review")}
                  >
                    Reviews
                  </button>
                  <button
                    className={`rounded-md px-3 py-1.5 text-sm transition-colors ${filter === "comment" ? "bg-primary text-white" : "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"}`}
                    onClick={() => setFilter("comment")}
                  >
                    Comments
                  </button>
                  <button
                    className={`rounded-md px-3 py-1.5 text-sm transition-colors ${filter === "shared" ? "bg-primary text-white" : "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"}`}
                    onClick={() => setFilter("shared")}
                  >
                    Shared
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Actions table */}
          <div className="overflow-x-auto rounded-lg bg-white shadow-md">
            <table className="w-full table-fixed divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    style={{ width: "12%" }}
                  >
                    Type
                  </th>
                  <th
                    scope="col"
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    onClick={() => handleSort("title")}
                    style={{ width: "25%" }}
                  >
                    <div className="flex items-center">
                      Title
                      {sortField === "title" && (
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={
                              sortDirection === "asc"
                                ? "M5 15l7-7 7 7"
                                : "M19 9l-7 7-7-7"
                            }
                          />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    onClick={() => handleSort("requester")}
                    style={{ width: "15%" }}
                  >
                    <div className="flex items-center">
                      Requester
                      {sortField === "requester" && (
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={
                              sortDirection === "asc"
                                ? "M5 15l7-7 7 7"
                                : "M19 9l-7 7-7-7"
                            }
                          />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    onClick={() => handleSort("date")}
                    style={{ width: "10%" }}
                  >
                    <div className="flex items-center">
                      Date
                      {sortField === "date" && (
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={
                              sortDirection === "asc"
                                ? "M5 15l7-7 7 7"
                                : "M19 9l-7 7-7-7"
                            }
                          />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    onClick={() => handleSort("priority")}
                    style={{ width: "10%" }}
                  >
                    <div className="flex items-center">
                      Priority
                      {sortField === "priority" && (
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={
                              sortDirection === "asc"
                                ? "M5 15l7-7 7 7"
                                : "M19 9l-7 7-7-7"
                            }
                          />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                    onClick={() => handleSort("status")}
                    style={{ width: "10%" }}
                  >
                    <div className="flex items-center">
                      Status
                      {sortField === "status" && (
                        <svg
                          className="ml-1 h-4 w-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={
                              sortDirection === "asc"
                                ? "M5 15l7-7 7 7"
                                : "M19 9l-7 7-7-7"
                            }
                          />
                        </svg>
                      )}
                    </div>
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase"
                    style={{ width: "18%" }}
                  >
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 bg-white">
                {paginatedData.map((item) => (
                  <tr key={item.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center text-gray-500">
                          {getTypeIcon(item.type)}
                        </div>
                        <div className="ml-2">
                          <span className="text-xs font-medium text-gray-900">
                            {getTypeLabel(item.type)}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm font-medium text-gray-900">
                        {item.title}
                      </div>
                      {item.description && (
                        <div className="line-clamp-2 text-xs text-gray-500">
                          {item.description}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {item.requester}
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.requesterRole}
                      </div>
                    </td>
                    <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-500">
                      {item.date}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`text-sm font-medium ${getPriorityColor(item.priority)}`}
                      >
                        {item.priority}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs leading-5 font-semibold ${getStatusBadgeColor(item.status)}`}
                      >
                        {item.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                      <button className="text-primary hover:text-primary mr-3">
                        View
                      </button>
                      <button className="text-primary hover:text-primary">
                        {item.type === "shared" ? "Open" : "Respond"}
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredSortedActions.length > 0 ? (
            <div className="mt-4 flex items-center justify-between rounded-lg border-t border-gray-200 bg-white px-4 py-3 shadow-md sm:px-6">
              <div className="flex flex-1 justify-between sm:hidden">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={() =>
                    setCurrentPage(Math.min(totalPages, currentPage + 1))
                  }
                  disabled={currentPage === totalPages}
                  className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Next
                </button>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    Showing{" "}
                    <span className="font-medium">
                      {Math.min(
                        filteredSortedActions.length,
                        (currentPage - 1) * itemsPerPage + 1
                      )}
                    </span>{" "}
                    to{" "}
                    <span className="font-medium">
                      {Math.min(
                        filteredSortedActions.length,
                        currentPage * itemsPerPage
                      )}
                    </span>{" "}
                    of{" "}
                    <span className="font-medium">
                      {filteredSortedActions.length}
                    </span>{" "}
                    results
                  </p>
                </div>
                <div>
                  <nav
                    className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm"
                    aria-label="Pagination"
                  >
                    <button
                      onClick={() =>
                        setCurrentPage(Math.max(1, currentPage - 1))
                      }
                      disabled={currentPage === 1}
                      className="relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <span className="sr-only">Previous</span>
                      <svg
                        className="h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      // Show pages around current page
                      let pageNum
                      if (totalPages <= 5) {
                        pageNum = i + 1
                      } else if (currentPage <= 3) {
                        pageNum = i + 1
                      } else if (currentPage >= totalPages - 2) {
                        pageNum = totalPages - 4 + i
                      } else {
                        pageNum = currentPage - 2 + i
                      }

                      return (
                        <button
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`relative inline-flex items-center border px-4 py-2 text-sm font-medium ${currentPage === pageNum ? "border-primary bg-primary z-10 text-white" : "border-gray-300 bg-white text-gray-500 hover:bg-gray-50"}`}
                        >
                          {pageNum}
                        </button>
                      )
                    })}
                    <button
                      onClick={() =>
                        setCurrentPage(Math.min(totalPages, currentPage + 1))
                      }
                      disabled={currentPage === totalPages}
                      className="relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <span className="sr-only">Next</span>
                      <svg
                        className="h-5 w-5"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                        aria-hidden="true"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          ) : (
            <div className="mt-4 rounded-lg bg-white py-12 text-center shadow-md">
              <p className="text-gray-500">
                No actions found matching your criteria.
              </p>
            </div>
          )}
        </div>
      </main>
    </>
  )
}

export default Actions
