import React from "react"

import <PERSON><PERSON>ontainer from "./ChartContainer"
import CommentableContent from "./CommentableContent"
import {
  AnalysisSection as AnalysisSectionType,
  QuarterlyResult,
} from "./types"

interface AnalysisSectionProps {
  section: AnalysisSectionType
  data?: QuarterlyResult[]
  onDrillDown?: () => void
  onRawDataView?: () => void
  showDataTable?: boolean
}

const AnalysisSection: React.FC<AnalysisSectionProps> = ({
  section,
  data,
  onDrillDown = () => {},
  onRawDataView = () => {},
  showDataTable = false,
}) => {
  return (
    <div className="mb-10">
      <h2 className="text-primary border-primary mb-4 border-b pb-2 text-2xl font-semibold dark:text-white">
        {section.title}
      </h2>
      <CommentableContent
        content={section.content}
        sectionId={section.id}
        className="mb-4 leading-relaxed text-black dark:text-white"
      />

      {section.id === "margin-analysis" && (
        <>
          {/* Chart */}
          <ChartContainer
            title="Quarterly Gross Margin Progression"
            onDrillDown={onDrillDown}
            data={data}
          />

          {/* Data Table */}
          {showDataTable && data && data.length > 0 && (
            <div className="mt-6 rounded-lg bg-white p-6 shadow-md">
              <div className="mb-6 flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    Quarterly Financial Data
                  </h3>
                  <p className="mt-1 text-sm text-gray-600">
                    Year-over-year revenue growth of{" "}
                    <span className="border-b-2 border-green-600 font-bold text-black">
                      25.3%
                    </span>{" "}
                    with consistent margin expansion across all quarters.
                    Revenue CAGR of{" "}
                    <span className="border-b-2 border-green-600 font-bold text-black">
                      7.8%
                    </span>{" "}
                    quarter-over-quarter indicates strong market position and
                    pricing power.
                  </p>
                </div>
                <div className="flex space-x-2">
                  <button className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50">
                    Export
                  </button>
                  <button
                    className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50"
                    onClick={onRawDataView}
                  >
                    Raw Data
                  </button>
                </div>
              </div>

              {/* High-finance style table */}
              <div className="overflow-hidden rounded border border-gray-200">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-3 py-2 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Period
                      </th>
                      <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Revenue
                      </th>
                      <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                        COGS
                      </th>
                      <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Gross Profit
                      </th>
                      <th className="px-3 py-2 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                        Margin
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {data.map((row, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-3 py-2 text-sm font-medium text-gray-900">
                          {row.period}
                        </td>
                        <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                          ${(row.revenue / 1000000).toFixed(1)}M
                        </td>
                        <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                          ${(row.cogs / 1000000).toFixed(1)}M
                        </td>
                        <td className="px-3 py-2 text-right text-sm font-medium text-gray-900">
                          ${(row.grossProfit / 1000000).toFixed(1)}M
                        </td>
                        <td className="px-3 py-2 text-right">
                          <span className="border-b-4 border-green-600 text-sm font-bold text-black">
                            {row.margin.toFixed(1)}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
                <div className="border-t border-gray-200 bg-gray-50 px-3 py-2">
                  <div className="space-y-1.5">
                    <p className="text-xs text-gray-500">
                      <span className="font-medium">Margin Analysis:</span>{" "}
                      Consistent improvement from{" "}
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        45.0%
                      </span>{" "}
                      to{" "}
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        48.0%
                      </span>{" "}
                      demonstrates successful execution of cost optimization
                      initiatives. Primary drivers include reduced raw material
                      costs (
                      <span className="border-b-2 border-red-600 font-bold text-black">
                        -12%
                      </span>
                      ) and increased production efficiency (
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        +15%
                      </span>
                      ).
                    </p>
                    <p className="text-xs text-gray-500">
                      <span className="font-medium">Revenue Growth:</span> Q3-Q4
                      acceleration (
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        +8.4%
                      </span>
                      ) outpaces industry average (
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        +5.2%
                      </span>
                      ) and reflects successful product mix shift toward
                      higher-margin offerings. Q4 performance exceeded target by{" "}
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        2.1
                      </span>{" "}
                      percentage points.
                    </p>
                    <p className="text-xs text-gray-500">
                      <span className="font-medium">Forward Outlook:</span>{" "}
                      Projected continuation of margin expansion in FY2025 with
                      target EBITDA margin of{" "}
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        20.5%
                      </span>{" "}
                      (
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        +1.8pp
                      </span>{" "}
                      YoY). Implementing cost reduction program for Product Line
                      C with estimated savings of{" "}
                      <span className="border-b-2 border-green-600 font-bold text-black">
                        $1.2M
                      </span>
                      .
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </>
      )}

      {section.id === "product-performance" && (
        <ChartContainer
          title="Product Line Contribution Margin"
          onDrillDown={onDrillDown}
        />
      )}

      {section.id === "ebitda-analysis" && (
        <ChartContainer title="EBITDA Trend" showDrillDown={false} />
      )}
    </div>
  )
}

export default AnalysisSection
