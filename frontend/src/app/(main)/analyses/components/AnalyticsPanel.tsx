import React, { useEffect, useState } from "react"

import ChartManager from "./ChartManager"
import { chartService } from "./chartService"
import { getProfitabilityCharts } from "./profitabilityCharts"
import SaveAnalysisModal from "./SaveAnalysisModal"
import {
  AnalysisType,
  Chart,
  Currency,
  DataSource,
  Filter,
  TimePeriod,
} from "./types"

interface AnalyticsPanelProps {
  visible: boolean
  onClose: () => void
  onRawDataView: () => void
  dataSources: DataSource[]
  activeFilters: Filter[]
  selectedAnalysisType: AnalysisType
  selectedCurrency: Currency
  selectedTimePeriod: TimePeriod
  selectedYear: string
  activeViewId: number
  analysisName: string
  onAnalysisTypeChange: (type: AnalysisType) => void
  onCurrencyChange: (currency: Currency) => void
  onTimePeriodChange: (period: TimePeriod) => void
  onYearChange: (year: string) => void
  onFilterRemove: (filterId: number) => void
  onFilterAdd?: (filter: Omit<Filter, "id">, dataSourceId: number) => void
  onApplyChanges: () => void
  onSaveAnalysis: (parentId: number, name: string) => void
  onChartsChange?: (charts: Chart[]) => void
}

const AnalyticsPanel: React.FC<AnalyticsPanelProps> = ({
  visible,
  onClose,
  onRawDataView,
  dataSources,
  activeFilters,
  selectedAnalysisType,
  selectedCurrency,
  selectedTimePeriod,
  selectedYear,
  activeViewId,
  analysisName,
  onAnalysisTypeChange,
  onCurrencyChange,
  onTimePeriodChange,
  onYearChange,
  onFilterRemove,
  onFilterAdd,
  onApplyChanges,
  onSaveAnalysis,
  onChartsChange,
}) => {
  const [filterDropdownOpen, setFilterDropdownOpen] = useState<number | null>(
    null
  )
  const [saveModalVisible, setSaveModalVisible] = useState(false)
  const [charts, setCharts] = useState<Chart[]>([])
  const [activeTab, setActiveTab] = useState<"filters" | "charts">("filters")

  // Load charts from database or use defaults
  useEffect(() => {
    const loadCharts = async () => {
      try {
        // Try to fetch charts from the database first
        const dbCharts = await chartService.getCharts(selectedAnalysisType)

        if (dbCharts && dbCharts.length > 0) {
          // Use charts from the database if available
          setCharts(dbCharts)
        } else {
          // Otherwise, use default charts
          let defaultCharts: Chart[] = []

          if (selectedAnalysisType === "profitability") {
            defaultCharts = getProfitabilityCharts()
          }
          // Add more conditions for other analysis types as needed

          setCharts(defaultCharts)

          // Save default charts to the database
          if (defaultCharts.length > 0) {
            await chartService.saveCharts(selectedAnalysisType, defaultCharts)
          }
        }
      } catch (error) {
        console.error("Error loading charts:", error)
        // Fallback to default charts if there's an error
        if (selectedAnalysisType === "profitability") {
          setCharts(getProfitabilityCharts())
        }
      }
    }

    loadCharts()
  }, [selectedAnalysisType])

  // Handle chart changes
  const handleChartsChange = async (updatedCharts: Chart[]) => {
    setCharts(updatedCharts)

    // Save changes to the database
    try {
      await chartService.saveCharts(selectedAnalysisType, updatedCharts)
    } catch (error) {
      console.error("Error saving chart changes:", error)
    }

    if (onChartsChange) {
      onChartsChange(updatedCharts)
    }
  }

  if (!visible) return null

  return (
    <div className="fixed top-0 right-0 z-10 h-full w-[32rem] overflow-y-auto border-l border-gray-200 bg-white shadow-lg">
      <div className="flex items-center justify-between border-b border-gray-200 p-4">
        <h3 className="text-primary text-lg font-medium">Analysis Controls</h3>
        <div className="flex items-center space-x-2">
          {/* Update button (icon) */}
          <button
            type="button"
            className="rounded-full bg-gray-100 p-2 text-gray-600 transition-colors hover:bg-gray-200 hover:text-gray-800"
            onClick={onApplyChanges}
            title="Update Analysis"
          >
            <span className="sr-only">Update Analysis</span>
            <svg
              className="h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
              />
            </svg>
          </button>

          {/* Save button (icon) */}
          <button
            type="button"
            className="rounded-full bg-gray-100 p-2 text-gray-600 transition-colors hover:bg-gray-200 hover:text-gray-800"
            onClick={() => setSaveModalVisible(true)}
            title="Save Analysis"
          >
            <span className="sr-only">Save Analysis</span>
            <svg
              className="h-4 w-4"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
              />
            </svg>
          </button>

          {/* Close button */}
          <button
            type="button"
            className="focus:ring-primary rounded-full p-1.5 text-gray-400 hover:bg-gray-100 hover:text-gray-500 focus:ring-2 focus:ring-offset-2 focus:outline-none"
            onClick={onClose}
          >
            <span className="sr-only">Close panel</span>
            <svg
              className="h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Tab Navigation - Prominent and always visible */}
      <div className="border-b border-gray-200 bg-gray-50">
        <div className="flex">
          <button
            className={`flex-1 px-4 py-3 text-base font-bold ${activeTab === "filters" ? "border-primary text-primary border-b-2 bg-white" : "text-gray-800 hover:text-black"}`}
            onClick={() => setActiveTab("filters")}
          >
            <div className="flex items-center justify-center">
              <svg
                className="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                />
              </svg>
              Filters & Measures
            </div>
          </button>
          <button
            className={`flex-1 px-4 py-3 text-base font-bold ${activeTab === "charts" ? "border-primary text-primary border-b-2 bg-white" : "text-gray-800 hover:text-black"}`}
            onClick={() => setActiveTab("charts")}
          >
            <div className="flex items-center justify-center">
              <svg
                className="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
              Charts
            </div>
          </button>
        </div>
      </div>

      {/* Chart Management Tab */}
      {activeTab === "charts" && (
        <div className="p-4">
          <ChartManager charts={charts} onChartsChange={handleChartsChange} />
        </div>
      )}

      {/* Filters Tab */}
      {activeTab === "filters" && (
        <>
          {/* Analysis Type and Currency Section */}
          <div className="border-b border-gray-200 p-4">
            <h4 className="mb-3 text-base font-bold tracking-wide text-black">
              FILTERS & MEASURES
            </h4>
            <div className="flex space-x-2">
              <div className="w-2/5">
                <select
                  className="focus:border-primary focus:ring-primary block w-full rounded-md border border-gray-300 px-3 py-2 text-base font-medium text-black shadow-sm focus:outline-none"
                  value={selectedAnalysisType}
                  onChange={(e) =>
                    onAnalysisTypeChange(e.target.value as AnalysisType)
                  }
                >
                  <option value="profitability">Profitability Analysis</option>
                  <option value="cashflow">Cash Flow Dashboard</option>
                  <option value="revenue">Revenue Analysis</option>
                  <option value="expense">Expense Analysis</option>
                  <option value="balance">Balance Sheet Health</option>
                  <option value="working-capital">
                    Working Capital Management
                  </option>
                  <option value="investment">Investment Analysis</option>
                  <option value="valuation">Valuation Metrics</option>
                  <option value="operational">Operational KPIs</option>
                  <option value="risk">Risk Assessment</option>
                </select>
              </div>
              <div className="w-1/5">
                <select
                  className="focus:border-primary focus:ring-primary block w-full rounded-md border border-gray-300 px-3 py-2 text-base font-medium text-black shadow-sm focus:outline-none"
                  value={selectedCurrency}
                  onChange={(e) => onCurrencyChange(e.target.value as Currency)}
                >
                  <option value="usd">USD</option>
                  <option value="sgd">SGD</option>
                  <option value="krw">KRW</option>
                  <option value="aud">AUD</option>
                </select>
              </div>
              <div className="w-1/5">
                <select
                  className="focus:border-primary focus:ring-primary block w-full rounded-md border border-gray-300 px-3 py-2 text-base font-medium text-black shadow-sm focus:outline-none"
                  value={selectedTimePeriod}
                  onChange={(e) =>
                    onTimePeriodChange(e.target.value as TimePeriod)
                  }
                >
                  <option value="quarterly">Quarterly</option>
                  <option value="monthly">Monthly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>
              <div className="w-1/5">
                <select
                  className="focus:border-primary focus:ring-primary block w-full rounded-md border border-gray-300 px-3 py-2 text-base font-medium text-black shadow-sm focus:outline-none"
                  value={selectedYear}
                  onChange={(e) => onYearChange(e.target.value)}
                >
                  <option value="2024">2024</option>
                  <option value="2023">2023</option>
                  <option value="2022">2022</option>
                  <option value="2021">2021</option>
                </select>
              </div>
            </div>
          </div>

          {/* Data Sources Section with Integrated Filters */}
          <div className="border-b border-gray-200 p-4">
            <h4 className="mb-3 text-base font-bold tracking-wide text-black">
              DATA SOURCES & FILTERS
            </h4>
            <div className="space-y-3">
              {dataSources.map((source) => {
                // Get filters for this specific data source
                const sourceFilters = activeFilters.filter(
                  (filter) => filter.dataSourceId === source.id
                )

                const handleAddFilter = (filterType: string) => {
                  if (onFilterAdd) {
                    // Generate a sample filter based on type
                    const filterColors = [
                      "#4F46E5",
                      "#10B981",
                      "#F59E0B",
                      "#EF4444",
                      "#8B5CF6",
                    ]
                    const randomColor =
                      filterColors[
                        Math.floor(Math.random() * filterColors.length)
                      ]

                    let filterValue = ""
                    switch (filterType) {
                      case "business_unit":
                        filterValue = "Business Unit Filter"
                        break
                      case "date_range":
                        filterValue = "Date Range Filter"
                        break
                      case "product_line":
                        filterValue = "Product Line Filter"
                        break
                      default:
                        filterValue = "New Filter"
                    }

                    onFilterAdd(
                      {
                        value: filterValue,
                        color: randomColor,
                        dataSourceId: source.id,
                      },
                      source.id
                    )
                  }
                  setFilterDropdownOpen(null)
                }

                return (
                  <div
                    key={source.id}
                    className="rounded-lg border border-gray-200 bg-white shadow-sm"
                  >
                    {/* Data Source Header */}
                    <div
                      className={`flex items-center justify-between rounded-t-lg px-4 py-3 ${source.color}`}
                    >
                      <span className="font-medium">{source.name}</span>
                      <button
                        className="rounded-full p-1.5 transition-colors hover:bg-black/5"
                        onClick={() =>
                          setFilterDropdownOpen(
                            filterDropdownOpen === source.id ? null : source.id
                          )
                        }
                        title="Add Filter"
                      >
                        <svg
                          className="h-4 w-4"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Filters for this data source */}
                    <div className="rounded-b-lg bg-gray-50 px-4 py-3">
                      {sourceFilters.length > 0 ? (
                        <div className="space-y-2">
                          {sourceFilters.map((filter) => (
                            <div
                              key={filter.id}
                              className="flex items-center justify-between rounded-md border border-gray-200 bg-white p-2"
                            >
                              <div className="flex items-center">
                                <span
                                  className="mr-2 h-3 w-3 rounded-full"
                                  style={{ backgroundColor: filter.color }}
                                ></span>
                                <span className="text-sm font-medium text-gray-900">
                                  {filter.value}
                                </span>
                              </div>
                              <button
                                className="text-gray-400 transition-colors hover:text-gray-600"
                                onClick={() => onFilterRemove(filter.id)}
                              >
                                <svg
                                  className="h-4 w-4"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                  stroke="currentColor"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M6 18L18 6M6 6l12 12"
                                  />
                                </svg>
                              </button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500 italic">
                          No filters applied
                        </p>
                      )}

                      {/* Filter dropdown for this specific data source */}
                      {filterDropdownOpen === source.id && (
                        <div className="z-10 mt-3 rounded-md border border-gray-200 bg-white shadow-lg">
                          <div className="py-1">
                            <button
                              className="block w-full rounded-md px-4 py-2 text-left text-sm font-medium text-gray-900 transition-colors hover:bg-gray-100"
                              onClick={() => handleAddFilter("business_unit")}
                            >
                              Business Unit
                            </button>
                            <button
                              className="block w-full rounded-md px-4 py-2 text-left text-sm font-medium text-gray-900 transition-colors hover:bg-gray-100"
                              onClick={() => handleAddFilter("date_range")}
                            >
                              Date Range
                            </button>
                            <button
                              className="block w-full rounded-md px-4 py-2 text-left text-sm font-medium text-gray-900 transition-colors hover:bg-gray-100"
                              onClick={() => handleAddFilter("product_line")}
                            >
                              Product Line
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </>
      )}

      {/* Raw Data Button */}
      <div className="space-y-3 p-4">
        <button
          className="flex w-full items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-black shadow-sm hover:bg-gray-50"
          onClick={onRawDataView}
        >
          <svg
            className="mr-2 -ml-1 h-5 w-5 text-gray-500"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 11h6"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 15h6"
            />
          </svg>
          View Raw Data
        </button>
      </div>

      {/* Save Analysis Modal */}
      <SaveAnalysisModal
        visible={saveModalVisible}
        onClose={() => setSaveModalVisible(false)}
        onSave={(name) => {
          onSaveAnalysis(activeViewId, name)
          setSaveModalVisible(false)
        }}
        parentAnalysisName={analysisName}
      />
    </div>
  )
}

export default AnalyticsPanel
