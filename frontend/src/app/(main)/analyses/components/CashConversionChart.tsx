import React from 'react';
import {
  Composed<PERSON><PERSON>,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';

interface CashConversionChartProps {
  data: {
    period: string;
    dso: number;
    dio: number;
    dpo: number;
    ccc: number;
  }[];
}

const CashConversionChart: React.FC<CashConversionChartProps> = ({ data }) => {
  // Calculate industry average for reference line
  const industryAverage = 54; // From our data narrative

  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={data}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 10,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="period" tick={{ fill: '#333' }} />
          <YAxis 
            yAxisId="left" 
            orientation="left" 
            tick={{ fill: '#333' }}
            label={{ value: 'Days', angle: -90, position: 'insideLeft', fill: '#333', offset: 5 }}
          />
          <Tooltip 
            contentStyle={{ backgroundColor: 'white', borderColor: '#ccc', color: '#333' }}
            formatter={(value: number) => [`${value} days`, '']}
            labelStyle={{ fontWeight: 'bold', color: '#333' }}
          />
          <Legend wrapperStyle={{ paddingTop: 10 }} />
          <Bar 
            yAxisId="left"
            dataKey="dso" 
            fill="#6c757d" 
            name="Days Sales Outstanding" 
            stackId="a"
          />
          <Bar 
            yAxisId="left"
            dataKey="dio" 
            fill="#adb5bd" 
            name="Days Inventory Outstanding" 
            stackId="a"
          />
          <Bar 
            yAxisId="left"
            dataKey="dpo" 
            fill="#dee2e6" 
            name="Days Payable Outstanding" 
            stackId="b"
          />
          <Line 
            yAxisId="left"
            type="monotone" 
            dataKey="ccc" 
            stroke="#0A2342" 
            strokeWidth={2}
            name="Cash Conversion Cycle" 
            dot={{ stroke: '#0A2342', strokeWidth: 2, r: 4, fill: 'white' }}
          />
          <ReferenceLine 
            yAxisId="left"
            y={industryAverage} 
            stroke="#d63384" 
            strokeDasharray="3 3" 
            label={{ 
              value: 'Industry Avg.', 
              position: 'insideBottomRight',
              fill: '#d63384'
            }} 
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default CashConversionChart;
