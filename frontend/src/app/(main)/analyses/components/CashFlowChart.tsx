import React from 'react';
import {
  Composed<PERSON>hart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer
} from 'recharts';

interface CashFlowChartProps {
  data: {
    period: string;
    operatingCashFlow: number;
    investingCashFlow: number;
    financingCashFlow: number;
    netCashFlow: number;
  }[];
}

const CashFlowChart: React.FC<CashFlowChartProps> = ({ data }) => {
  // Format data for display
  const formattedData = data.map(item => ({
    period: item.period,
    operatingCashFlow: item.operatingCashFlow / 1000000,
    investingCashFlow: item.investingCashFlow / 1000000,
    financingCashFlow: item.financingCashFlow / 1000000,
    netCashFlow: item.netCashFlow / 1000000
  }));

  return (
    <div className="w-full h-80">
      <ResponsiveContainer width="100%" height="100%">
        <ComposedChart
          data={formattedData}
          margin={{
            top: 20,
            right: 30,
            left: 20,
            bottom: 10,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
          <XAxis dataKey="period" tick={{ fill: '#333' }} />
          <YAxis 
            yAxisId="left" 
            orientation="left" 
            tick={{ fill: '#333' }}
            label={{ value: 'Cash Flow ($M)', angle: -90, position: 'insideLeft', fill: '#333', offset: 5 }}
          />
          <Tooltip 
            contentStyle={{ backgroundColor: 'white', borderColor: '#ccc', color: '#333' }}
            formatter={(value: number) => [`$${value.toFixed(1)}M`, '']}
            labelStyle={{ fontWeight: 'bold', color: '#333' }}
          />
          <Legend wrapperStyle={{ paddingTop: 10 }} />
          <Bar 
            yAxisId="left"
            dataKey="operatingCashFlow" 
            fill="#0A2342" 
            name="Operating Cash Flow" 
          />
          <Bar 
            yAxisId="left"
            dataKey="investingCashFlow" 
            fill="#6c757d" 
            name="Investing Cash Flow" 
          />
          <Bar 
            yAxisId="left"
            dataKey="financingCashFlow" 
            fill="#adb5bd" 
            name="Financing Cash Flow" 
          />
          <Line 
            yAxisId="left"
            type="monotone" 
            dataKey="netCashFlow" 
            stroke="#2EC4B6" 
            strokeWidth={2}
            name="Net Cash Flow" 
            dot={{ stroke: '#2EC4B6', strokeWidth: 2, r: 4, fill: 'white' }}
          />
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  );
};

export default CashFlowChart;
