import React, { useEffect, useState } from "react"
import { v4 as uuidv4 } from "uuid"

import ChartPreview from "./ChartPreview"
import { Chart, ChartDimension, ChartType } from "./types"

interface ChartConstructorModalProps {
  visible: boolean
  onClose: () => void
  onSave: (chart: Chart) => void
  initialChart?: Chart
  chartOrder: number
  availableFields?: {
    id: string
    name: string
    type: "category" | "measure" | "time"
    table?: string
  }[]
  availableTables?: { id: string; name: string; description?: string }[]
}

const ChartConstructorModal: React.FC<ChartConstructorModalProps> = ({
  visible,
  onClose,
  onSave,
  initialChart,
  chartOrder,
  availableFields = [],
  availableTables = [],
}) => {
  const [chartName, setChartName] = useState("")
  const [chartType, setChartType] = useState<ChartType>("bar")
  const [secondaryChartType, setSecondaryChartType] = useState<ChartType | "">(
    ""
  )
  const [useCombinedChart, setUseCombinedChart] = useState(false)
  const [selectedTables, setSelectedTables] = useState<string[]>([])
  const [xAxis, setXAxis] = useState<string>("")
  const [yAxis, setYAxis] = useState<string[]>([])
  const [colorBy, setColorBy] = useState<string>("")
  const [showLegend, setShowLegend] = useState(true)
  const [showGrid, setShowGrid] = useState(true)
  const [showValues, setShowValues] = useState(false)
  const [showTrendline, setShowTrendline] = useState(false)
  const [chartDescription, setChartDescription] = useState("")

  // Reset form when modal opens/closes or initialChart changes
  useEffect(() => {
    if (visible) {
      if (initialChart) {
        // Edit mode - populate form with existing chart data
        setChartName(initialChart.name)
        setChartType(initialChart.type)
        setChartDescription(initialChart.description || "")

        // Set data tables if available
        if (initialChart.dataTables && initialChart.dataTables.length > 0) {
          setSelectedTables(initialChart.dataTables)
        } else if (initialChart.dataTable) {
          // For backward compatibility
          setSelectedTables([initialChart.dataTable])
        }

        // Set dimensions
        if (initialChart.dimensions.x) {
          setXAxis(initialChart.dimensions.x.id)
        }

        if (initialChart.dimensions.y) {
          setYAxis(initialChart.dimensions.y.map((y) => y.id))
        }

        if (initialChart.dimensions.color) {
          setColorBy(initialChart.dimensions.color.id)
        }

        // Check if this is a combined chart
        if (initialChart.secondaryChartType) {
          setSecondaryChartType(initialChart.secondaryChartType)
          setUseCombinedChart(true)
        }

        // Set settings
        if (initialChart.settings) {
          setShowLegend(initialChart.settings.showLegend || false)
          setShowGrid(initialChart.settings.showGrid || false)
          setShowValues(initialChart.settings.showValues || false)
          setShowTrendline(initialChart.settings.showTrendline || false)
        }
      } else {
        // Create mode - set defaults
        setChartName("")
        setChartType("bar")
        setSecondaryChartType("")
        setUseCombinedChart(false)
        setSelectedTables([])
        setXAxis("")
        setYAxis([])
        setColorBy("")
        setShowLegend(true)
        setShowGrid(true)
        setShowValues(false)
        setShowTrendline(false)
        setChartDescription("")
      }
    }
  }, [visible, initialChart])

  const handleSave = () => {
    // Find the corresponding field objects
    const xAxisField = availableFields.find((f) => f.id === xAxis)
    const yAxisFields = yAxis
      .map((y) => availableFields.find((f) => f.id === y))
      .filter(Boolean) as {
      id: string
      name: string
      type: "category" | "measure" | "time"
    }[]
    const colorField = availableFields.find((f) => f.id === colorBy)

    // Create dimension objects
    const xDimension: ChartDimension | undefined = xAxisField
      ? {
          id: xAxisField.id,
          name: xAxisField.name,
          field: xAxisField.id,
          type: xAxisField.type,
        }
      : undefined

    const yDimensions: ChartDimension[] = yAxisFields.map((field) => ({
      id: field.id,
      name: field.name,
      field: field.id,
      type: field.type,
    }))

    const colorDimension: ChartDimension | undefined = colorField
      ? {
          id: colorField.id,
          name: colorField.name,
          field: colorField.id,
          type: colorField.type,
        }
      : undefined

    // Create chart object
    const chart: Chart = {
      id: initialChart?.id || uuidv4(),
      name:
        chartName ||
        `New ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart`,
      type: chartType,
      secondaryChartType: useCombinedChart
        ? (secondaryChartType as ChartType)
        : undefined,
      order: chartOrder,
      dataTables: selectedTables,
      dimensions: {
        x: xDimension,
        y: yDimensions,
        color: colorDimension,
      },
      settings: {
        showLegend,
        showGrid,
        showValues,
        showTrendline,
      },
      description: chartDescription,
    }

    onSave(chart)
    onClose()
  }

  // Get available fields based on type and selected tables
  const getFieldsByType = (
    type: "category" | "measure" | "time"
  ): { id: string; name: string }[] => {
    if (selectedTables.length === 0) return []

    return availableFields
      .filter(
        (field) =>
          field.type === type &&
          (!field.table || selectedTables.includes(field.table))
      )
      .map((field) => ({
        id: field.id,
        name: field.name,
      }))
  }

  // Create preview dimensions for the chart preview
  const getPreviewDimensions = () => {
    const dimensions: {
      x?: ChartDimension
      y?: ChartDimension[]
      color?: ChartDimension
    } = {}

    // Find the corresponding field objects
    const xAxisField = availableFields.find((f) => f.id === xAxis)
    const yAxisFields = yAxis
      .map((y) => availableFields.find((f) => f.id === y))
      .filter(Boolean) as {
      id: string
      name: string
      type: "category" | "measure" | "time"
    }[]
    const colorField = availableFields.find((f) => f.id === colorBy)

    // Create dimension objects
    if (xAxisField) {
      dimensions.x = {
        id: xAxisField.id,
        name: xAxisField.name,
        field: xAxisField.id,
        type: xAxisField.type,
      }
    }

    if (yAxisFields.length > 0) {
      dimensions.y = yAxisFields.map((field) => ({
        id: field.id,
        name: field.name,
        field: field.id,
        type: field.type,
      }))
    }

    if (colorField) {
      dimensions.color = {
        id: colorField.id,
        name: colorField.name,
        field: colorField.id,
        type: colorField.type,
      }
    }

    return dimensions
  }

  if (!visible) return null

  return (
    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center overflow-y-auto bg-black p-4">
      <div className="max-h-[90vh] w-full max-w-6xl overflow-y-auto rounded-lg bg-white shadow-xl">
        <div className="p-6">
          <div className="mb-6 flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              {initialChart ? "Edit Chart" : "Create New Chart"}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          <div className="mb-6">
            <h3 className="mb-4 text-lg font-bold text-black">
              Select Data Tables
            </h3>
            <div className="mb-4 flex flex-wrap gap-2">
              {availableTables.map((table) => (
                <button
                  key={table.id}
                  onClick={() => {
                    if (selectedTables.includes(table.id)) {
                      setSelectedTables(
                        selectedTables.filter((id) => id !== table.id)
                      )
                    } else {
                      setSelectedTables([...selectedTables, table.id])
                    }
                  }}
                  className={`rounded-full px-3 py-1 text-sm font-medium ${
                    selectedTables.includes(table.id)
                      ? "border border-blue-300 bg-blue-100 text-blue-800"
                      : "border border-gray-300 bg-gray-100 text-gray-800 hover:bg-gray-200"
                  }`}
                >
                  {table.name}
                </button>
              ))}
            </div>

            {selectedTables.length > 0 && (
              <p className="mt-1 text-sm text-gray-600">
                Selected {selectedTables.length} tables. Chart preview will
                update automatically as you make selections.
              </p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-6">
            {/* Left side: Chart settings */}
            <div>
              <div className="mb-6 rounded-md border border-gray-200 bg-white p-4">
                <h3 className="mb-4 text-lg font-bold text-black">
                  Chart Type
                </h3>

                <div className="mb-4">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Chart Name
                  </label>
                  <input
                    type="text"
                    value={chartName}
                    onChange={(e) => setChartName(e.target.value)}
                    placeholder="Enter chart name"
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                  />
                </div>

                <div className="mt-4">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Primary Chart Type
                  </label>
                  <select
                    value={chartType}
                    onChange={(e) => setChartType(e.target.value as ChartType)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                  >
                    <option value="bar">Bar Chart</option>
                    <option value="line">Line Chart</option>
                    <option value="area">Area Chart</option>
                    <option value="pie">Pie Chart</option>
                    <option value="scatter">Scatter Plot</option>
                    <option value="waterfall">Waterfall Chart</option>
                    <option value="boxplot">Box Plot</option>
                    <option value="heatmap">Heat Map</option>
                    <option value="treemap">Tree Map</option>
                    <option value="radar">Radar Chart</option>
                  </select>
                </div>

                <div className="mt-4 flex items-center">
                  <input
                    type="checkbox"
                    id="useCombinedChart"
                    checked={useCombinedChart}
                    onChange={(e) => setUseCombinedChart(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor="useCombinedChart"
                    className="ml-2 block text-sm text-gray-700"
                  >
                    Use Combined Chart (Multiple Dimensions)
                  </label>
                </div>

                {useCombinedChart && (
                  <div className="mt-4">
                    <label className="mb-1 block text-sm font-medium text-gray-700">
                      Secondary Chart Type
                    </label>
                    <select
                      value={secondaryChartType}
                      onChange={(e) =>
                        setSecondaryChartType(e.target.value as ChartType)
                      }
                      className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                    >
                      <option value="">Select Secondary Chart Type</option>
                      <option value="bar">Bar Chart</option>
                      <option value="line">Line Chart</option>
                      <option value="area">Area Chart</option>
                    </select>
                  </div>
                )}
              </div>

              <div className="mb-6 rounded-md border border-gray-200 bg-white p-4">
                <h3 className="mb-4 text-lg font-bold text-black">
                  Data Dimensions
                </h3>

                <div className="mb-4">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    X-Axis Dimension
                  </label>
                  <select
                    value={xAxis}
                    onChange={(e) => setXAxis(e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                    disabled={selectedTables.length === 0}
                  >
                    <option value="">Select X-Axis Dimension</option>
                    {getFieldsByType("category").map((field) => (
                      <option key={field.id} value={field.id}>
                        {field.name}
                      </option>
                    ))}
                    {getFieldsByType("time").map((field) => (
                      <option key={field.id} value={field.id}>
                        {field.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Usually a time or category field
                  </p>
                </div>

                <div className="mb-4">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Y-Axis Dimension(s)
                  </label>
                  <select
                    multiple
                    value={yAxis}
                    onChange={(e) => {
                      const options = Array.from(
                        e.target.selectedOptions,
                        (option) => option.value
                      )
                      setYAxis(options)
                    }}
                    className="h-32 w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                    disabled={selectedTables.length === 0}
                  >
                    {getFieldsByType("measure").map((field) => (
                      <option key={field.id} value={field.id}>
                        {field.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Hold Ctrl/Cmd to select multiple measures
                  </p>
                </div>

                <div className="mb-4">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Color Dimension (Optional)
                  </label>
                  <select
                    value={colorBy}
                    onChange={(e) => setColorBy(e.target.value)}
                    className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                    disabled={selectedTables.length === 0}
                  >
                    <option value="">Select Color Dimension</option>
                    {getFieldsByType("category").map((field) => (
                      <option key={field.id} value={field.id}>
                        {field.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Usually a category field
                  </p>
                </div>
              </div>

              <div className="rounded-md border border-gray-200 bg-white p-4">
                <h3 className="mb-4 text-lg font-bold text-black">
                  Chart Settings
                </h3>

                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showLegend"
                      checked={showLegend}
                      onChange={(e) => setShowLegend(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="showLegend"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Show Legend
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showGrid"
                      checked={showGrid}
                      onChange={(e) => setShowGrid(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="showGrid"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Show Grid
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showValues"
                      checked={showValues}
                      onChange={(e) => setShowValues(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="showValues"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Show Values
                    </label>
                  </div>

                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="showTrendline"
                      checked={showTrendline}
                      onChange={(e) => setShowTrendline(e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <label
                      htmlFor="showTrendline"
                      className="ml-2 block text-sm text-gray-700"
                    >
                      Show Trendline
                    </label>
                  </div>
                </div>

                <div className="mt-4">
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Chart Description (Optional)
                  </label>
                  <textarea
                    value={chartDescription}
                    onChange={(e) => setChartDescription(e.target.value)}
                    placeholder="Add a description for this chart..."
                    className="h-24 w-full rounded-md border border-gray-300 px-3 py-2 text-sm text-gray-900 shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Right side: Chart preview */}
            <div className="rounded-md border border-gray-200 bg-white">
              <h3 className="border-b border-gray-200 p-4 text-lg font-bold text-black">
                Live Preview
              </h3>
              <div
                className="flex-grow overflow-hidden p-4"
                style={{ height: "350px" }}
              >
                <ChartPreview
                  chartType={chartType}
                  secondaryChartType={
                    useCombinedChart
                      ? (secondaryChartType as ChartType)
                      : undefined
                  }
                  dimensions={getPreviewDimensions()}
                  settings={{
                    showLegend,
                    showGrid,
                    showValues,
                    showTrendline,
                    title:
                      chartName ||
                      `New ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart`,
                  }}
                />
              </div>
              <div className="border-t border-gray-200 p-4 text-xs text-gray-500">
                <p>
                  This is a preview with sample data. The actual chart will use
                  your real data.
                </p>
              </div>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-3 border-t border-gray-200 pt-4">
            <button
              onClick={onClose}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={selectedTables.length === 0}
              className={`rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white shadow-sm focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none ${
                selectedTables.length === 0
                  ? "cursor-not-allowed bg-gray-400"
                  : "bg-blue-600 hover:bg-blue-700"
              }`}
            >
              {initialChart ? "Update Chart" : "Create Chart"}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChartConstructorModal
