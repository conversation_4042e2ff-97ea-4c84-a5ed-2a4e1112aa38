import React, { useState } from "react"

import ChartConstructorModal from "./ChartConstructorModal"
import { deleteChart, reorderCharts, updateChart } from "./profitabilityCharts"
import { Chart } from "./types"

interface ChartManagerProps {
  charts: Chart[]
  onChartsChange: (charts: Chart[]) => void
  availableFields?: {
    id: string
    name: string
    type: "time" | "measure" | "category"
  }[]
}

const ChartManager: React.FC<ChartManagerProps> = ({
  charts,
  onChartsChange,
  availableFields = [],
}) => {
  const [draggedChartId, setDraggedChartId] = useState<string | null>(null)
  const [dropTargetId, setDropTargetId] = useState<string | null>(null)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingChart, setEditingChart] = useState<Chart | null>(null)

  // Handle chart deletion
  const handleDeleteChart = (chartId: string) => {
    const updatedCharts = deleteChart(charts, chartId)
    onChartsChange(updatedCharts)
  }

  // Handle opening edit modal for a chart
  const handleEditChart = (chart: Chart) => {
    setEditingChart(chart)
    setShowEditModal(true)
  }

  // Handle saving an edited chart
  const handleSaveEditedChart = (updatedChart: Chart) => {
    const updatedCharts = updateChart(charts, updatedChart)
    onChartsChange(updatedCharts)
    setShowEditModal(false)
    setEditingChart(null)
  }

  // Handle saving a new chart
  const handleSaveNewChart = (newChart: Chart) => {
    const updatedCharts = [...charts, newChart]
    onChartsChange(updatedCharts)
    setShowAddModal(false)
  }

  // Handle drag start
  const handleDragStart = (chartId: string) => {
    setDraggedChartId(chartId)
  }

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, chartId: string) => {
    e.preventDefault()
    setDropTargetId(chartId)
  }

  // Handle drag leave
  const handleDragLeave = () => {
    setDropTargetId(null)
  }

  // Handle drag end
  const handleDragEnd = () => {
    setDraggedChartId(null)
    setDropTargetId(null)
  }

  // Handle drop to reorder
  const handleDrop = (targetChartId: string) => {
    if (draggedChartId && draggedChartId !== targetChartId) {
      const draggedIndex = charts.findIndex(
        (chart) => chart.id === draggedChartId
      )
      const targetIndex = charts.findIndex(
        (chart) => chart.id === targetChartId
      )

      if (draggedIndex !== -1 && targetIndex !== -1) {
        const reorderedCharts = reorderCharts(charts, draggedIndex, targetIndex)
        onChartsChange(reorderedCharts)
      }
    }
    setDraggedChartId(null)
    setDropTargetId(null)
  }

  // Get available fields for chart dimensions
  const getAvailableFields = (): {
    id: string
    name: string
    type: "time" | "measure" | "category"
    table?: string
  }[] => {
    if (availableFields && availableFields.length > 0) {
      return availableFields
    }

    // Fields specific to each Django model
    const modelFields = {
      // Financial Data fields
      FinancialData: [
        {
          id: "fiscal_year",
          name: "Fiscal Year",
          type: "time" as const,
          table: "FinancialData",
        },
        {
          id: "fiscal_quarter",
          name: "Fiscal Quarter",
          type: "time" as const,
          table: "FinancialData",
        },
        {
          id: "date",
          name: "Date",
          type: "time" as const,
          table: "FinancialData",
        },
        {
          id: "value",
          name: "Value",
          type: "measure" as const,
          table: "FinancialData",
        },
        {
          id: "data_type",
          name: "Data Type",
          type: "category" as const,
          table: "FinancialData",
        },
        {
          id: "location",
          name: "Location",
          type: "category" as const,
          table: "FinancialData",
        },
        {
          id: "doctor",
          name: "Doctor",
          type: "category" as const,
          table: "FinancialData",
        },
        {
          id: "geography",
          name: "Geography",
          type: "category" as const,
          table: "FinancialData",
        },
        {
          id: "revenue_category",
          name: "Revenue Category",
          type: "category" as const,
          table: "FinancialData",
        },
        {
          id: "expense_category",
          name: "Expense Category",
          type: "category" as const,
          table: "FinancialData",
        },
      ],

      // Staff Headcount fields
      StaffHeadcount: [
        {
          id: "fiscal_year",
          name: "Fiscal Year",
          type: "time" as const,
          table: "StaffHeadcount",
        },
        {
          id: "fiscal_quarter",
          name: "Fiscal Quarter",
          type: "time" as const,
          table: "StaffHeadcount",
        },
        {
          id: "date",
          name: "Date",
          type: "time" as const,
          table: "StaffHeadcount",
        },
        {
          id: "headcount",
          name: "Headcount",
          type: "measure" as const,
          table: "StaffHeadcount",
        },
        {
          id: "fte_count",
          name: "FTE Count",
          type: "measure" as const,
          table: "StaffHeadcount",
        },
        {
          id: "staff_type",
          name: "Staff Type",
          type: "category" as const,
          table: "StaffHeadcount",
        },
        {
          id: "location",
          name: "Location",
          type: "category" as const,
          table: "StaffHeadcount",
        },
        {
          id: "geography",
          name: "Geography",
          type: "category" as const,
          table: "StaffHeadcount",
        },
      ],

      // Doctor fields
      Doctor: [
        {
          id: "first_name",
          name: "First Name",
          type: "category" as const,
          table: "Doctor",
        },
        {
          id: "last_name",
          name: "Last Name",
          type: "category" as const,
          table: "Doctor",
        },
        {
          id: "doctor_id",
          name: "Doctor ID",
          type: "category" as const,
          table: "Doctor",
        },
        {
          id: "specialties",
          name: "Specialties",
          type: "category" as const,
          table: "Doctor",
        },
        {
          id: "primary_location",
          name: "Primary Location",
          type: "category" as const,
          table: "Doctor",
        },
        {
          id: "employment_status",
          name: "Employment Status",
          type: "category" as const,
          table: "Doctor",
        },
        {
          id: "join_date",
          name: "Join Date",
          type: "time" as const,
          table: "Doctor",
        },
        {
          id: "base_salary",
          name: "Base Salary",
          type: "measure" as const,
          table: "Doctor",
        },
        {
          id: "commission_rate",
          name: "Commission Rate",
          type: "measure" as const,
          table: "Doctor",
        },
      ],

      // Location fields
      Location: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "Location",
        },
        {
          id: "code",
          name: "Code",
          type: "category" as const,
          table: "Location",
        },
        {
          id: "geography",
          name: "Geography",
          type: "category" as const,
          table: "Location",
        },
        {
          id: "opening_date",
          name: "Opening Date",
          type: "time" as const,
          table: "Location",
        },
        {
          id: "square_footage",
          name: "Square Footage",
          type: "measure" as const,
          table: "Location",
        },
        {
          id: "num_treatment_rooms",
          name: "Treatment Rooms",
          type: "measure" as const,
          table: "Location",
        },
      ],

      // Other models with key fields
      RevenueCategory: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "RevenueCategory",
        },
        {
          id: "code",
          name: "Code",
          type: "category" as const,
          table: "RevenueCategory",
        },
        {
          id: "parent_category",
          name: "Parent Category",
          type: "category" as const,
          table: "RevenueCategory",
        },
      ],

      ExpenseCategory: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "ExpenseCategory",
        },
        {
          id: "code",
          name: "Code",
          type: "category" as const,
          table: "ExpenseCategory",
        },
        {
          id: "parent_category",
          name: "Parent Category",
          type: "category" as const,
          table: "ExpenseCategory",
        },
      ],

      Geography: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "Geography",
        },
        {
          id: "code",
          name: "Code",
          type: "category" as const,
          table: "Geography",
        },
        {
          id: "currency",
          name: "Currency",
          type: "category" as const,
          table: "Geography",
        },
      ],

      FiscalYear: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "FiscalYear",
        },
        {
          id: "start_date",
          name: "Start Date",
          type: "time" as const,
          table: "FiscalYear",
        },
        {
          id: "end_date",
          name: "End Date",
          type: "time" as const,
          table: "FiscalYear",
        },
      ],

      FiscalQuarter: [
        {
          id: "quarter_number",
          name: "Quarter Number",
          type: "category" as const,
          table: "FiscalQuarter",
        },
        {
          id: "fiscal_year",
          name: "Fiscal Year",
          type: "category" as const,
          table: "FiscalQuarter",
        },
        {
          id: "start_date",
          name: "Start Date",
          type: "time" as const,
          table: "FiscalQuarter",
        },
        {
          id: "end_date",
          name: "End Date",
          type: "time" as const,
          table: "FiscalQuarter",
        },
      ],

      FinancialMetric: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "FinancialMetric",
        },
        {
          id: "code",
          name: "Code",
          type: "category" as const,
          table: "FinancialMetric",
        },
        {
          id: "data_type",
          name: "Data Type",
          type: "category" as const,
          table: "FinancialMetric",
        },
      ],

      BusinessImpactEvent: [
        {
          id: "name",
          name: "Name",
          type: "category" as const,
          table: "BusinessImpactEvent",
        },
        {
          id: "event_type",
          name: "Event Type",
          type: "category" as const,
          table: "BusinessImpactEvent",
        },
        {
          id: "start_date",
          name: "Start Date",
          type: "time" as const,
          table: "BusinessImpactEvent",
        },
        {
          id: "end_date",
          name: "End Date",
          type: "time" as const,
          table: "BusinessImpactEvent",
        },
        {
          id: "estimated_revenue_impact",
          name: "Revenue Impact",
          type: "measure" as const,
          table: "BusinessImpactEvent",
        },
        {
          id: "estimated_expense_impact",
          name: "Expense Impact",
          type: "measure" as const,
          table: "BusinessImpactEvent",
        },
      ],
    }

    // Flatten all model fields into a single array
    return Object.values(modelFields).flat()
  }

  // Get available data tables (Django ORM)
  const getAvailableTables = (): {
    id: string
    name: string
    description?: string
  }[] => {
    // These are the actual Django models from the backend
    return [
      {
        id: "FinancialData",
        name: "Financial Data",
        description:
          "Core financial metrics including actuals, budget, and forecasts",
      },
      {
        id: "StaffHeadcount",
        name: "Staff Headcount",
        description:
          "Staff headcount data across different staff types and locations",
      },
      {
        id: "Doctor",
        name: "Doctor Data",
        description:
          "Doctor information including specialties and compensation",
      },
      {
        id: "Location",
        name: "Location Data",
        description: "Clinic and business location information",
      },
      {
        id: "RevenueCategory",
        name: "Revenue Categories",
        description: "Revenue categorization hierarchy",
      },
      {
        id: "ExpenseCategory",
        name: "Expense Categories",
        description: "Expense categorization hierarchy",
      },
      {
        id: "Geography",
        name: "Geography Data",
        description: "Geographical regions for business operations",
      },
      {
        id: "FiscalYear",
        name: "Fiscal Years",
        description: "Fiscal year periods for financial reporting",
      },
      {
        id: "FiscalQuarter",
        name: "Fiscal Quarters",
        description: "Quarterly periods within fiscal years",
      },
      {
        id: "FinancialMetric",
        name: "Financial Metrics",
        description:
          "Definitions of financial metrics (e.g., EBITDA, Margin %)",
      },
      {
        id: "BusinessImpactEvent",
        name: "Business Impact Events",
        description: "Significant events affecting business performance",
      },
    ]
  }

  return (
    <div className="chart-manager rounded-md bg-white p-4 shadow-sm">
      <div className="mb-4 border-b border-gray-200 pb-2">
        <h3 className="text-base font-bold tracking-wider text-black uppercase">
          CHART SEQUENCE
        </h3>
        <p className="mt-1 text-sm font-medium text-gray-900">
          Drag and drop to reorder charts. The order determines the narrative
          flow.
        </p>
      </div>

      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-xl font-bold text-black">Chart Management</h3>
        <button
          className="bg-primary hover:bg-primary focus:ring-primary rounded-md px-4 py-2 text-base font-medium text-white focus:ring-2 focus:ring-offset-2 focus:outline-none"
          onClick={() => setShowAddModal(true)}
        >
          Add Chart
        </button>
      </div>

      {/* Chart list */}
      <div className="space-y-2">
        {charts.map((chart) => (
          <div
            key={chart.id}
            className={`flex items-center border p-3 ${draggedChartId === chart.id ? "border-primary bg-blue-50" : dropTargetId === chart.id ? "border-primary border-dashed bg-blue-50" : "border-gray-300 bg-white"} mb-2 cursor-move rounded-md shadow-sm transition-colors duration-200`}
            draggable
            onDragStart={() => handleDragStart(chart.id)}
            onDragOver={(e) => handleDragOver(e, chart.id)}
            onDragLeave={handleDragLeave}
            onDragEnd={handleDragEnd}
            onDrop={() => handleDrop(chart.id)}
          >
            <div className="mr-3 cursor-grab text-gray-500 active:cursor-grabbing">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </div>
            <div className="flex-grow">
              <div className="flex items-center">
                <span className="mr-2 w-6 text-center text-base font-bold">
                  {chart.order}.
                </span>
                <span className="text-base font-medium text-black">
                  {chart.name}
                </span>
                <span className="ml-2 rounded-full bg-gray-100 px-2 py-0.5 text-sm font-medium text-black">
                  {chart.type}
                </span>
                {chart.secondaryChartType && (
                  <span className="ml-2 rounded-full bg-blue-100 px-2 py-0.5 text-sm font-medium text-black">
                    + {chart.secondaryChartType}
                  </span>
                )}
              </div>
            </div>
            <div className="flex space-x-1">
              <div className="flex">
                <button
                  className="hover:text-primary rounded-full p-2 text-gray-800 hover:bg-gray-100"
                  onClick={() => handleEditChart(chart)}
                  title="Edit Chart"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                    />
                  </svg>
                </button>
                <button
                  className="ml-1 rounded-full p-2 text-gray-800 hover:bg-gray-100 hover:text-red-500"
                  onClick={() => handleDeleteChart(chart.id)}
                  title="Delete Chart"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {charts.length === 0 && (
        <div className="py-4 text-center text-gray-500">
          No charts added yet. Click {`"Add Chart"`} to create your first chart.
        </div>
      )}

      {/* Modals */}
      {showEditModal && editingChart && (
        <ChartConstructorModal
          visible={showEditModal}
          onClose={() => setShowEditModal(false)}
          onSave={handleSaveEditedChart}
          initialChart={editingChart}
          chartOrder={editingChart.order}
          availableFields={getAvailableFields()}
          availableTables={getAvailableTables()}
        />
      )}

      {showAddModal && (
        <ChartConstructorModal
          visible={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSave={handleSaveNewChart}
          chartOrder={charts.length + 1}
          availableFields={getAvailableFields()}
          availableTables={getAvailableTables()}
        />
      )}
    </div>
  )
}

export default ChartManager
