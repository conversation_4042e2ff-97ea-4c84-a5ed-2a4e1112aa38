import React, { useEffect, useState } from "react"
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  Pie<PERSON>hart,
  PolarAngleAxis,
  PolarGrid,
  PolarRadiusAxis,
  Radar,
  RadarChart,
  ReferenceLine,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>atter<PERSON>hart,
  Tooltip,
  Treemap,
  XAxis,
  YAxis,
} from "recharts"

import { ChartDimension, ChartType } from "./types"

interface ChartPreviewProps {
  chartType: ChartType
  secondaryChartType?: ChartType
  dimensions: {
    x?: ChartDimension
    y?: ChartDimension[]
    color?: ChartDimension
  }
  settings: {
    showLegend?: boolean
    showGrid?: boolean
    showValues?: boolean
    showTrendline?: boolean
    title?: string
  }
}

// Sample data for preview
const generateSampleData = (
  xDimension?: ChartDimension,
  yDimensions?: ChartDimension[]
) => {
  // Always generate some sample data even if dimensions aren't set
  const categories = ["<PERSON>", "Feb", "<PERSON>", "Apr", "May", "Jun"]

  if (!yDimensions || yDimensions.length === 0) {
    // Default sample data if no Y dimensions are set
    return categories.map((category, index) => ({
      name: category,
      value: 100 + Math.round(Math.sin(index) * 100),
      value2: 200 + Math.round(Math.cos(index) * 100),
    }))
  }

  // Generate data based on the provided dimensions
  return categories.map((category, index) => {
    const dataPoint: Record<string, unknown> = { name: category }

    yDimensions.forEach((dim, dimIndex) => {
      // Generate some random but consistent data
      const baseValue = 100 + dimIndex * 50
      const randomFactor = Math.sin(index * (dimIndex + 1)) * 100
      dataPoint[dim.id] = Math.round(baseValue + randomFactor)
    })

    return dataPoint
  })
}

// Colors for the chart
const COLORS = [
  "#0088FE",
  "#00C49F",
  "#FFBB28",
  "#FF8042",
  "#8884d8",
  "#82ca9d",
]

const ChartPreview: React.FC<ChartPreviewProps> = ({
  chartType,
  secondaryChartType,
  dimensions,
  settings,
}) => {
  const [data, setData] = useState<Record<string, unknown>[]>([])

  useEffect(() => {
    // Generate sample data based on dimensions
    setData(generateSampleData(dimensions.x, dimensions.y))
  }, [dimensions])

  // Always show some kind of preview, even if dimensions aren't fully set
  const sampleData = generateSampleData(dimensions.x, dimensions.y)

  if (sampleData.length === 0) {
    return (
      <div className="flex h-full items-center justify-center rounded-md bg-gray-50 p-4">
        <p className="text-center text-gray-500">
          Select dimensions to preview chart
        </p>
      </div>
    )
  }

  const renderChart = () => {
    // Always use 'name' as the default x-axis key for sample data
    const xAxisKey = "name"

    switch (chartType) {
      case "bar":
        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={xAxisKey} />
              <YAxis domain={["auto", "auto"]} />
              <Tooltip />
              {settings.showLegend && <Legend />}

              {dimensions.y?.map((dim, index) => (
                <Bar
                  key={dim.id}
                  dataKey={dim.id}
                  name={dim.name}
                  fill={COLORS[index % COLORS.length]}
                />
              )) || <Bar dataKey="value" name="Value" fill={COLORS[0]} />}

              {/* Render secondary chart if it exists */}
              {secondaryChartType === "line" &&
                dimensions.y &&
                dimensions.y.length > 0 && (
                  <Line
                    type="monotone"
                    dataKey={dimensions.y[0].id}
                    stroke="#ff7300"
                    yAxisId={0}
                  />
                )}
            </BarChart>
          </ResponsiveContainer>
        )

      case "line":
        return (
          <ResponsiveContainer width="100%" height={300}>
            <LineChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={xAxisKey} />
              <YAxis domain={["auto", "auto"]} />
              <Tooltip />
              {settings.showLegend && <Legend />}

              {dimensions.y?.map((dim, index) => (
                <Line
                  key={dim.id}
                  type="monotone"
                  dataKey={dim.id}
                  name={dim.name}
                  stroke={COLORS[index % COLORS.length]}
                />
              ))}

              {/* Render secondary chart if it exists */}
              {secondaryChartType === "bar" &&
                dimensions.y &&
                dimensions.y.length > 0 && (
                  <Bar dataKey={dimensions.y[0].id} fill="#413ea0" />
                )}
            </LineChart>
          </ResponsiveContainer>
        )

      case "area":
        return (
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart
              data={data}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={xAxisKey} />
              <YAxis domain={["auto", "auto"]} />
              <Tooltip />
              {settings.showLegend && <Legend />}

              {dimensions.y?.map((dim, index) => (
                <Area
                  key={dim.id}
                  type="monotone"
                  dataKey={dim.id}
                  name={dim.name}
                  fill={COLORS[index % COLORS.length]}
                  stroke={COLORS[index % COLORS.length]}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        )

      case "pie":
        // For pie charts, we need to transform the data
        const pieData = dimensions.y?.map((dim, index) => ({
          name: dim.name,
          value: data.reduce(
            (sum, item) => sum + (Number(item[dim.id]) || 0),
            0
          ),
          fill: COLORS[index % COLORS.length],
        }))

        return (
          <ResponsiveContainer width="100%" height={300}>
            <PieChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={settings.showValues}
                label={settings.showValues ? (entry) => entry.name : undefined}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData?.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.fill} />
                ))}
              </Pie>
              <Tooltip />
              {settings.showLegend && <Legend />}
            </PieChart>
          </ResponsiveContainer>
        )

      case "scatter":
        return (
          <ResponsiveContainer width="100%" height={300}>
            <ScatterChart margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey={xAxisKey} />
              <YAxis domain={["auto", "auto"]} />
              <Tooltip />
              {settings.showLegend && <Legend />}

              {dimensions.y?.map((dim, index) => (
                <Scatter
                  key={dim.id}
                  name={dim.name}
                  data={data.map((item) => ({
                    x: item[xAxisKey],
                    y: item[dim.id],
                  }))}
                  fill={COLORS[index % COLORS.length]}
                />
              ))}
            </ScatterChart>
          </ResponsiveContainer>
        )

      case "waterfall":
        // Utility function to prepare data for waterfall chart
        const prepareWaterfallData = () => {
          // Initial value (starting point)
          const start = 1000

          // Define the changes (deltas) in each period
          const changes = [
            { name: "Start", value: start, isInitial: true },
            { name: "Revenue", value: 500 },
            { name: "Costs", value: -300 },
            { name: "Marketing", value: -150 },
            { name: "Tax", value: -50 },
          ]

          // Create the waterfall data structure
          const result = []
          let runningTotal = start

          // First item (Start)
          result.push({
            name: "Start",
            value: start,
            isInitial: true,
            // For the bar chart
            start: 0,
            end: start,
          })

          // Process each change item
          for (let i = 1; i < changes.length; i++) {
            const item = changes[i]
            const previousTotal = runningTotal
            runningTotal += item.value

            // Following waterfall chart rules:
            // - For positive values: start from previous total, end at new total
            // - For negative values: start from new total, end at previous total
            if (item.value >= 0) {
              // Positive change - goes upward from previous total
              result.push({
                name: item.name,
                value: item.value,
                start: previousTotal, // Start from previous total
                end: runningTotal, // End at new total (higher)
              })
            } else {
              // Negative change - goes downward from previous total
              result.push({
                name: item.name,
                value: item.value,
                start: runningTotal, // Start from new total (lower)
                end: previousTotal, // End at previous total (higher)
              })
            }
          }

          // Add the final total
          result.push({
            name: "End",
            value: runningTotal,
            isSum: true,
            // For the bar chart
            start: 0,
            end: runningTotal,
          })

          return result
        }

        const waterfallData = prepareWaterfallData()

        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={waterfallData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis domain={["auto", "auto"]} />
              <Tooltip
                formatter={(value: number | string) => [`${value}`, "Value"]}
                labelFormatter={(name: string) => `${name}`}
              />
              {settings.showLegend && <Legend />}

              {/* Bars for start values */}
              <Bar
                dataKey="start"
                stackId="a"
                fill="transparent"
                stroke="transparent"
              />

              {/* Bars for end values - these create the waterfall effect */}
              <Bar
                dataKey="end"
                stackId="a"
                name="Value"
                fill="#4CAF50" // Default fill, will be overridden by cell colors
              >
                {/* Use Cell components to color individual bars */}
                {waterfallData.map((entry, index) => {
                  let color
                  if (entry.isInitial) {
                    color = "#8884d8" // Purple for start
                  } else if (entry.isSum) {
                    color = "#82ca9d" // Green for end total
                  } else {
                    // Use green for positive changes and red for negative changes
                    // per user preference for profitability analysis
                    color = entry.value >= 0 ? "#4CAF50" : "#F44336" // Green for positive, red for negative
                  }
                  return <Cell key={`cell-${index}`} fill={color} />
                })}
              </Bar>

              <ReferenceLine y={0} stroke="#000" />
            </BarChart>
          </ResponsiveContainer>
        )

      case "boxplot":
        // Generate boxplot data with quartiles
        const boxplotData = [
          { name: "Group A", min: 20, q1: 40, median: 50, q3: 60, max: 80 },
          { name: "Group B", min: 30, q1: 45, median: 55, q3: 70, max: 90 },
          { name: "Group C", min: 10, q1: 30, median: 45, q3: 65, max: 75 },
        ]

        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={boxplotData}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
            >
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis dataKey="name" />
              <YAxis domain={["auto", "auto"]} />
              <Tooltip />
              {settings.showLegend && <Legend />}

              {/* Min to Max line */}
              <Bar dataKey="min" fill="transparent" stroke="#8884d8" />
              <Bar dataKey="max" fill="transparent" stroke="#8884d8" />

              {/* Box for Q1 to Q3 */}
              <Bar dataKey="q1" stackId="a" fill="#8884d8" />
              <Bar dataKey="median" stackId="a" fill="#82ca9d" />
              <Bar dataKey="q3" stackId="a" fill="#ffc658" />
            </BarChart>
          </ResponsiveContainer>
        )

      case "heatmap":
        // Generate heatmap data
        const heatmapData = []
        const rows = 7
        const cols = 5

        for (let i = 0; i < rows; i++) {
          for (let j = 0; j < cols; j++) {
            const value = Math.floor(Math.random() * 100)
            heatmapData.push({
              x: `Col ${j + 1}`,
              y: `Row ${i + 1}`,
              value,
            })
          }
        }

        // Group data by y-axis for rendering
        const groupedData = heatmapData.reduce(
          (acc: Record<string, Record<string, unknown>>, item) => {
            if (!acc[item.y]) {
              acc[item.y] = {}
            }
            acc[item.y][item.x] = item.value
            acc[item.y].name = item.y
            return acc
          },
          {}
        )

        const heatmapRows = Object.values(groupedData)

        return (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={heatmapRows}
              margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              layout="vertical"
            >
              {settings.showGrid && <CartesianGrid strokeDasharray="3 3" />}
              <XAxis type="number" />
              <YAxis dataKey="name" type="category" />
              <Tooltip />
              {settings.showLegend && <Legend />}

              {Array.from({ length: cols }).map((_, index) => {
                const colName = `Col ${index + 1}`
                return (
                  <Bar
                    key={colName}
                    dataKey={colName}
                    fill={`hsl(${240 - index * 30}, 70%, 60%)`}
                    name={colName}
                  />
                )
              })}
            </BarChart>
          </ResponsiveContainer>
        )

      case "treemap":
        // Generate treemap data
        const treemapData = [
          {
            name: "Category A",
            children: [
              { name: "A1", size: 100 },
              { name: "A2", size: 200 },
              { name: "A3", size: 150 },
            ],
          },
          {
            name: "Category B",
            children: [
              { name: "B1", size: 120 },
              { name: "B2", size: 80 },
              { name: "B3", size: 170 },
            ],
          },
          {
            name: "Category C",
            children: [
              { name: "C1", size: 90 },
              { name: "C2", size: 110 },
            ],
          },
        ]

        return (
          <ResponsiveContainer width="100%" height={300}>
            <Treemap
              data={treemapData}
              dataKey="size"
              stroke="#fff"
              fill="#8884d8"
            />
          </ResponsiveContainer>
        )

      case "radar":
        // Generate radar data
        const radarData = [
          { subject: "Math", A: 120, B: 110, fullMark: 150 },
          { subject: "English", A: 98, B: 130, fullMark: 150 },
          { subject: "Physics", A: 86, B: 130, fullMark: 150 },
          { subject: "Chemistry", A: 99, B: 100, fullMark: 150 },
          { subject: "Biology", A: 85, B: 90, fullMark: 150 },
          { subject: "History", A: 65, B: 85, fullMark: 150 },
        ]

        return (
          <ResponsiveContainer width="100%" height={300}>
            <RadarChart cx="50%" cy="50%" outerRadius="80%" data={radarData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="subject" />
              <PolarRadiusAxis angle={30} domain={[0, 150]} />
              <Radar
                name="Student A"
                dataKey="A"
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.6}
              />
              <Radar
                name="Student B"
                dataKey="B"
                stroke="#82ca9d"
                fill="#82ca9d"
                fillOpacity={0.6}
              />
              <Tooltip />
              {settings.showLegend && <Legend />}
            </RadarChart>
          </ResponsiveContainer>
        )

      default:
        return (
          <div className="flex h-full items-center justify-center rounded-md bg-gray-50">
            <p className="text-gray-500">
              {chartType} chart preview not implemented
            </p>
          </div>
        )
    }
  }

  return (
    <div className="chart-preview h-full">
      {settings.title && (
        <h3 className="mb-2 text-center text-base font-bold">
          {settings.title}
        </h3>
      )}
      <div className="h-[calc(100%-30px)]">{renderChart()}</div>
    </div>
  )
}

export default ChartPreview
