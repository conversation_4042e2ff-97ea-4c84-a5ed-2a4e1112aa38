import React, { useState } from 'react';

interface EditAnalysisModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (name: string) => void;
  initialName: string;
}

const EditAnalysisModal: React.FC<EditAnalysisModalProps> = ({
  isOpen,
  onClose,
  onSave,
  initialName
}) => {
  const [name, setName] = useState(initialName);

  if (!isOpen) return null;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onSave(name);
    }
  };

  return (
    <div className="fixed inset-0 z-[200] overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-[var(--navy)] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="px-4 pt-5 pb-4 sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <h3 className="text-lg leading-6 font-medium text-white">
                    Edit Analysis
                  </h3>
                  <div className="mt-4">
                    <label htmlFor="analysis-name" className="block text-sm font-medium text-gray-300">
                      Analysis Name
                    </label>
                    <input
                      type="text"
                      name="analysis-name"
                      id="analysis-name"
                      className="mt-1 block w-full border border-gray-600 bg-[var(--navy-light)] rounded-md shadow-sm py-2 px-3 text-white focus:outline-none focus:ring-[var(--turquoise)] focus:border-[var(--turquoise)]"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      autoFocus
                    />
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[var(--navy-light)] px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-[var(--turquoise)] text-base font-medium text-white hover:bg-[var(--turquoise-dark)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--turquoise)] sm:ml-3 sm:w-auto sm:text-sm"
              >
                Save
              </button>
              <button
                type="button"
                className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-[var(--navy)] text-base font-medium text-gray-300 hover:bg-[var(--navy-lighter)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                onClick={onClose}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default EditAnalysisModal;
