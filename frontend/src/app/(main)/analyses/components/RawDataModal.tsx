import React, { useEffect } from "react"

import { ExtendedRowData, Filter } from "./types"

interface RawDataModalProps {
  visible: boolean
  onClose: () => void
  tableData: ExtendedRowData[]
  activeFilters: Filter[]
  isLoading: boolean
  hasMore: boolean
  onLoadMore: () => void
  onFilterRemove: (filterId: number) => void
}

const RawDataModal: React.FC<RawDataModalProps> = ({
  visible,
  onClose,
  tableData,
  activeFilters,
  isLoading,
  hasMore,
  onLoadMore,
  onFilterRemove,
}) => {
  // Set up intersection observer for infinite scrolling
  useEffect(() => {
    if (!visible) return

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !isLoading) {
          onLoadMore()
        }
      },
      { threshold: 0.5 }
    )

    const target = document.getElementById("infinite-scroll-trigger")
    if (target) observer.observe(target)

    return () => {
      if (target) observer.unobserve(target)
    }
  }, [visible, onLoadMore, hasMore, isLoading])

  if (!visible) return null

  return (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* Overlay */}
      <div
        className="bg-opacity-50 fixed inset-0 bg-black transition-opacity"
        onClick={onClose}
      ></div>

      {/* Modal container */}
      <div className="fixed inset-0 flex items-center justify-center p-4">
        {/* Modal content */}
        <div className="flex h-[90vh] w-[90%] flex-col overflow-hidden rounded-lg bg-white shadow-xl">
          {/* Modal header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <div className="flex items-center">
              <h2 className="text-primary text-xl font-semibold">Raw Data</h2>
              <span className="ml-4 rounded-full bg-gray-100 px-3 py-1 text-sm text-gray-700">
                {tableData.length} rows{" "}
                {isLoading && (
                  <span className="ml-1 animate-pulse">loading...</span>
                )}
              </span>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>

          {/* Modal body */}
          <div className="flex flex-1 overflow-hidden">
            {/* Sidebar */}
            <div className="w-64 overflow-y-auto border-r border-gray-200 bg-gray-50 p-4">
              <h3 className="mb-4 text-sm font-medium text-gray-700">
                DATA FILTERS
              </h3>

              <div className="space-y-4">
                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Measure
                  </label>
                  <select className="focus:border-primary focus:ring-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none">
                    <option>Revenue</option>
                    <option>Gross Profit</option>
                    <option>EBITDA</option>
                    <option>Net Income</option>
                  </select>
                </div>

                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Time Period
                  </label>
                  <select className="focus:border-primary focus:ring-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none">
                    <option>Q1 2024 - Q4 2024</option>
                    <option>Q1 2024 - Q2 2024</option>
                    <option>Q3 2024 - Q4 2024</option>
                    <option>FY 2024</option>
                  </select>
                </div>

                <div>
                  <label className="mb-1 block text-sm font-medium text-gray-700">
                    Data Source
                  </label>
                  <select className="focus:border-primary focus:ring-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none">
                    <option>All Sources</option>
                    <option>Financial Database</option>
                    <option>CRM Data</option>
                    <option>ERP System</option>
                  </select>
                </div>

                <div className="border-t border-gray-200 pt-4">
                  <h4 className="mb-3 text-sm font-medium text-gray-700">
                    Active Filters
                  </h4>
                  <div className="space-y-2">
                    {activeFilters.map((filter) => (
                      <div
                        key={filter.id}
                        className="flex items-center justify-between rounded-md border border-gray-200 bg-white p-2"
                      >
                        <div className="flex items-center">
                          <span
                            className="mr-2 h-3 w-3 rounded-full"
                            style={{ backgroundColor: filter.color }}
                          ></span>
                          <span className="text-sm text-gray-700">
                            {filter.value}
                          </span>
                        </div>
                        <button
                          className="text-gray-400 hover:text-gray-600"
                          onClick={() => onFilterRemove(filter.id)}
                        >
                          <svg
                            className="h-4 w-4"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M6 18L18 6M6 6l12 12"
                            />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="pt-4">
                  <button className="bg-primary hover:bg-primary flex w-full items-center justify-center rounded-md border border-transparent px-4 py-2 text-sm font-medium text-white shadow-sm">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Main content */}
            <div className="flex flex-1 flex-col overflow-hidden">
              {/* Table container */}
              <div className="flex-1 overflow-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="sticky top-0 z-10 bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        Period
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        Revenue
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        COGS
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        Gross Profit
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        Margin %
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        Source
                      </th>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                      >
                        Business Unit
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {tableData.map((row, index) => {
                      let sourceColor = "bg-blue-100 text-blue-800"

                      if (row.source === "CRM Data") {
                        sourceColor = "bg-green-100 text-green-800"
                      } else if (row.source === "ERP System") {
                        sourceColor = "bg-orange-100 text-orange-800"
                      }

                      return (
                        <tr key={index} className="hover:bg-gray-50">
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            {row.period}
                          </td>
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            ${row.revenue.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            ${row.cogs.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            ${row.grossProfit.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            {row.margin.toFixed(1)}%
                          </td>
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            <span
                              className={`inline-flex rounded-full px-2 text-xs leading-5 font-semibold ${sourceColor}`}
                            >
                              {row.source || "Financial Database"}
                            </span>
                          </td>
                          <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900">
                            {row.unit || "All"}
                          </td>
                        </tr>
                      )
                    })}
                  </tbody>
                </table>

                {/* Infinite scroll loading indicator */}
                {hasMore && (
                  <div
                    className="flex justify-center py-4"
                    id="infinite-scroll-trigger"
                  >
                    <div className="flex items-center space-x-2 text-sm text-gray-500">
                      <svg
                        className="text-primary h-5 w-5 animate-spin"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      <span>Loading more data...</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="flex items-center justify-between border-t border-gray-200 bg-gray-50 px-6 py-3">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-700">
                    Showing {tableData.length} rows
                  </span>
                  <div className="h-5 border-l border-gray-300"></div>
                  <span className="text-sm text-gray-700">
                    Last updated: May 8, 2025
                  </span>
                </div>
                <div className="flex space-x-3">
                  <button className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none">
                    <svg
                      className="mr-2 -ml-1 h-5 w-5 text-gray-500"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                      />
                    </svg>
                    Export Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RawDataModal
