import React, { useState } from "react"

interface SaveAnalysisModalProps {
  visible: boolean
  onClose: () => void
  onSave: (name: string) => void
  parentAnalysisName: string
}

const SaveAnalysisModal: React.FC<SaveAnalysisModalProps> = ({
  visible,
  onClose,
  onSave,
  parentAnalysisName,
}) => {
  const [analysisName, setAnalysisName] = useState("")
  const [error, setError] = useState("")

  if (!visible) return null

  const handleSave = (e: React.FormEvent) => {
    e.preventDefault() // Prevent form submission from refreshing the page

    if (!analysisName.trim()) {
      setError("Analysis name is required")
      return
    }

    onSave(analysisName)
    setAnalysisName("")
    setError("")
  }

  return (
    <div
      className="fixed inset-0 z-[9999] overflow-y-auto"
      style={{ pointerEvents: "auto" }}
    >
      <div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Overlay */}
        <div
          className="fixed inset-0 transition-opacity"
          aria-hidden="true"
          onClick={onClose}
        >
          <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>

        <span
          className="hidden sm:inline-block sm:h-screen sm:align-middle"
          aria-hidden="true"
        >
          &#8203;
        </span>

        {/* Modal content */}
        <div
          className="inline-block transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:align-middle"
          style={{ position: "relative", zIndex: 10000 }}
        >
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mt-3 w-full text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3
                  className="text-lg leading-6 font-medium text-gray-900"
                  id="modal-title"
                >
                  Save Analysis
                </h3>
                <div className="mt-2">
                  <p className="mb-4 text-sm text-gray-500">
                    Save a custom version of &ldquo;{parentAnalysisName}&rdquo;
                    with your current filters and measures.
                  </p>
                  <p className="text-xs text-gray-400 italic">
                    Note: Saved analysis will appear under the &ldquo;
                    {parentAnalysisName}&rdquo; item in the navigation sidebar.
                  </p>
                  <div className="mt-4">
                    <label
                      htmlFor="analysis-name"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Analysis Name
                    </label>
                    <input
                      type="text"
                      name="analysis-name"
                      id="analysis-name"
                      className={`mt-1 block w-full border ${error ? "border-red-500" : "border-gray-300"} rounded-md px-3 py-2 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 focus:outline-none sm:text-sm`}
                      placeholder="Enter a name for your analysis"
                      value={analysisName}
                      onChange={(e) => {
                        setAnalysisName(e.target.value)
                        if (e.target.value.trim()) setError("")
                      }}
                      autoFocus
                    />
                    {error && (
                      <p className="mt-1 text-sm text-red-500">{error}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <form onSubmit={handleSave}>
            <div className="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
              <button
                type="submit"
                className="bg-primary hover:bg-primary inline-flex w-full justify-center rounded-md border border-transparent px-4 py-2 text-base font-medium text-white shadow-sm focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm"
              >
                Save Analysis
              </button>
              <button
                type="button"
                className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                onClick={onClose}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default SaveAnalysisModal
