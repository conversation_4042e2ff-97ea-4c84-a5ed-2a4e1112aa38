import React, { useRef, useState } from "react"

interface ShareModalProps {
  visible: boolean
  onClose: () => void
  analysisTitle: string
  templateType?: string // 'BASE' or 'Snapshot'
  createdAt?: string // Date when the analysis was created
}

interface EmailChip {
  id: string
  email: string
}

const ShareModal: React.FC<ShareModalProps> = ({
  visible,
  onClose,
  analysisTitle,
  templateType = "BASE",
  createdAt = new Date().toISOString(),
}) => {
  const [shareOption, setShareOption] = useState<"link" | "pdf">("link")
  const [emailInput, setEmailInput] = useState("")
  const [emailChips, setEmailChips] = useState<EmailChip[]>([])
  const [message, setMessage] = useState("")
  const [sendStatus, setSendStatus] = useState<"idle" | "sending" | "sent">(
    "idle"
  )

  // Text formatting state
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Function to apply formatting to selected text
  const applyFormatting = (formatType: string) => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const selectedText = textarea.value.substring(start, end)

    if (start === end) return // No text selected

    let formattedText = ""
    const beforeText = textarea.value.substring(0, start)
    const afterText = textarea.value.substring(end)

    switch (formatType) {
      case "bold":
        formattedText = `**${selectedText}**`
        break
      case "italic":
        formattedText = `_${selectedText}_`
        break
      case "underline":
        formattedText = `<u>${selectedText}</u>`
        break
      case "bullet":
        formattedText = selectedText
          .split("\n")
          .map((line: string) => `• ${line}`)
          .join("\n")
        break
      case "number":
        formattedText = selectedText
          .split("\n")
          .map((line: string, i: number) => `${i + 1}. ${line}`)
          .join("\n")
        break
      case "left":
        formattedText = `<div style="text-align: left">${selectedText}</div>`
        break
      case "center":
        formattedText = `<div style="text-align: center">${selectedText}</div>`
        break
      case "right":
        formattedText = `<div style="text-align: right">${selectedText}</div>`
        break
      default:
        formattedText = selectedText
    }

    setMessage(beforeText + formattedText + afterText)

    // Focus back on textarea and set selection after the formatted text
    setTimeout(() => {
      textarea.focus()
      const newCursorPos = start + formattedText.length
      textarea.setSelectionRange(newCursorPos, newCursorPos)
    }, 0)
  }

  // Handle share action
  const handleShare = () => {
    // Set status to sending
    setSendStatus("sending")

    // Simulate API call with a timeout
    setTimeout(() => {
      // In a real implementation, this would handle the sharing action
      console.log(
        "Sharing with:",
        emailChips.map((chip) => chip.email)
      )
      console.log("Message:", message)
      console.log("Share option:", shareOption)

      // Set status to sent
      setSendStatus("sent")

      // Reset status and close modal after a delay
      setTimeout(() => {
        setSendStatus("idle")
        onClose()
      }, 1000)
    }, 1500)
  }

  // Format date for display
  const formattedDate = new Date(createdAt).toLocaleDateString(undefined, {
    year: "numeric",
    month: "long",
    day: "numeric",
  })

  if (!visible) return null

  return (
    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
      <div className="w-full max-w-2xl overflow-hidden rounded-lg bg-white shadow-xl">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 p-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Share Analysis
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 transition-colors hover:text-gray-600"
            aria-label="Close"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="p-6">
          {/* Analysis title with full details - styled like the report */}
          <div className="border-primary mb-6 border-b pb-3">
            {/* <h3 className="text-lg font-semibold text-primary mb-3">Sharing Analysis:</h3> */}
            <div className="space-y-2 pl-1">
              <p className="text-base">
                <span className="text-primary font-medium">Name:</span>{" "}
                <span className="ml-2 text-black">{analysisTitle}</span>
              </p>
              <p className="text-base">
                <span className="text-primary font-medium">Template:</span>{" "}
                <span className="ml-2 text-black">{templateType}</span>
              </p>
              <p className="text-base">
                <span className="text-primary font-medium">Date:</span>{" "}
                <span className="ml-2 text-black">{formattedDate}</span>
              </p>
            </div>
          </div>

          {/* Share options */}
          <div className="mb-5">
            <div className="flex overflow-hidden rounded-md border border-gray-200">
              <button
                className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${shareOption === "link" ? "bg-blue-100 text-blue-800" : "bg-white text-gray-700 hover:bg-gray-50"}`}
                onClick={() => setShareOption("link")}
              >
                Share Link
              </button>
              <button
                className={`flex-1 px-4 py-2 text-sm font-medium transition-colors ${shareOption === "pdf" ? "bg-blue-100 text-blue-800" : "bg-white text-gray-700 hover:bg-gray-50"}`}
                onClick={() => setShareOption("pdf")}
              >
                Send PDF
              </button>
            </div>
          </div>

          {/* Email input with chips */}
          <div className="mb-5">
            <label className="mb-2 block text-sm font-medium text-gray-800">
              Recipient Emails
            </label>
            <div className="flex flex-wrap items-center gap-2 rounded-md border border-gray-300 bg-white p-2">
              {emailChips.map((chip) => (
                <div
                  key={chip.id}
                  className="flex items-center rounded-md bg-blue-100 px-2 py-1 text-blue-800"
                >
                  <span className="text-sm font-medium">{chip.email}</span>
                  <button
                    type="button"
                    className="ml-1 text-blue-600 hover:text-blue-800"
                    onClick={() =>
                      setEmailChips((chips) =>
                        chips.filter((c) => c.id !== chip.id)
                      )
                    }
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-4 w-4"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>
                </div>
              ))}
              <input
                type="email"
                className="min-w-[150px] flex-1 border-0 p-1 text-black focus:ring-0 focus:outline-none"
                placeholder={
                  emailChips.length > 0
                    ? "Add more recipients..."
                    : "Enter email addresses (separate with space, tab, or semicolon)"
                }
                style={{ color: "black" }}
                value={emailInput}
                onChange={(e) => setEmailInput(e.target.value)}
                onKeyDown={(e) => {
                  // Add chip on Tab, Space, Enter, or Semicolon
                  if (
                    ["Tab", "Enter"].includes(e.key) ||
                    e.key === " " ||
                    e.key === ";"
                  ) {
                    e.preventDefault()
                    const email = emailInput.trim().replace(";", "")
                    if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                      setEmailChips((chips) => [
                        ...chips,
                        { id: Date.now().toString(), email },
                      ])
                      setEmailInput("")
                    }
                  }
                }}
                onBlur={() => {
                  // Add chip on blur if there's valid input
                  const email = emailInput.trim().replace(";", "")
                  if (email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                    setEmailChips((chips) => [
                      ...chips,
                      { id: Date.now().toString(), email },
                    ])
                    setEmailInput("")
                  }
                }}
              />
            </div>
            <p className="mt-1 text-xs text-gray-600">
              Press Tab, Space, Enter, or type semicolon to add multiple
              recipients
            </p>
          </div>

          {/* Message textarea with formatting options */}
          <div className="mb-6">
            <label className="mb-2 block text-sm font-medium text-gray-800">
              Message (Optional)
            </label>
            <div className="overflow-hidden rounded-md border border-gray-300">
              {/* Simple formatting toolbar */}
              <div className="flex items-center gap-2 border-b border-gray-300 bg-white p-2">
                <div className="mr-1 flex border-r border-gray-300 pr-2">
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Bold"
                    onClick={() => applyFormatting("bold")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M4 2.5H8.5C9.12 2.5 9.72 2.75 10.16 3.19C10.61 3.63 10.86 4.23 10.86 4.85C10.86 5.47 10.61 6.07 10.16 6.51C10.72 6.95 11.07 7.63 11.07 8.35C11.07 9.07 10.77 9.75 10.27 10.24C9.77 10.74 9.09 11 8.36 11H4V2.5ZM5.71 6H8C8.27 6 8.52 5.89 8.71 5.71C8.89 5.52 9 5.27 9 5C9 4.73 8.89 4.48 8.71 4.29C8.52 4.11 8.27 4 8 4H5.71V6ZM5.71 9.5H8C8.4 9.5 8.78 9.34 9.06 9.06C9.34 8.78 9.5 8.4 9.5 8C9.5 7.6 9.34 7.22 9.06 6.94C8.78 6.66 8.4 6.5 8 6.5H5.71V9.5Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Italic"
                    onClick={() => applyFormatting("italic")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6 3H12L11.5 4.5H9.5L8 11.5H10L9.5 13H3.5L4 11.5H6L7.5 4.5H5.5L6 3Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Underline"
                    onClick={() => applyFormatting("underline")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M4 2.5V7.5C4 8.56 4.42 9.58 5.17 10.33C5.92 11.08 6.94 11.5 8 11.5C9.06 11.5 10.08 11.08 10.83 10.33C11.58 9.58 12 8.56 12 7.5V2.5H10.5V7.5C10.5 8.16 10.24 8.8 9.77 9.27C9.3 9.74 8.66 10 8 10C7.34 10 6.7 9.74 6.23 9.27C5.76 8.8 5.5 8.16 5.5 7.5V2.5H4Z"
                        fill="currentColor"
                      />
                      <path d="M3 13H13V14.5H3V13Z" fill="currentColor" />
                    </svg>
                  </button>
                </div>

                <div className="mr-1 flex border-r border-gray-300 pr-2">
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Bullet List"
                    onClick={() => applyFormatting("bullet")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M5.5 4H14V5.5H5.5V4ZM5.5 7.25H14V8.75H5.5V7.25ZM5.5 10.5H14V12H5.5V10.5ZM2 4.75C2 4.55 2.04 4.35 2.12 4.17C2.2 3.99 2.32 3.83 2.46 3.71C2.61 3.58 2.78 3.48 2.96 3.42C3.15 3.36 3.35 3.34 3.54 3.37C3.73 3.4 3.92 3.47 4.08 3.58C4.24 3.69 4.38 3.83 4.48 4C4.58 4.17 4.64 4.36 4.66 4.55C4.68 4.75 4.65 4.94 4.58 5.12C4.51 5.31 4.4 5.47 4.26 5.61C4.12 5.74 3.95 5.84 3.77 5.9C3.58 5.96 3.38 5.98 3.19 5.95C3 5.92 2.81 5.85 2.65 5.74C2.49 5.63 2.35 5.49 2.25 5.32C2.15 5.15 2.09 4.96 2.07 4.77C2.07 4.76 2.07 4.76 2.07 4.75H2ZM2 8C2 7.8 2.04 7.61 2.12 7.43C2.2 7.25 2.32 7.09 2.46 6.96C2.61 6.83 2.78 6.73 2.96 6.67C3.15 6.61 3.35 6.59 3.54 6.62C3.73 6.65 3.92 6.72 4.08 6.83C4.24 6.94 4.38 7.08 4.48 7.25C4.58 7.42 4.64 7.61 4.66 7.8C4.68 8 4.65 8.19 4.58 8.37C4.51 8.56 4.4 8.72 4.26 8.86C4.12 8.99 3.95 9.09 3.77 9.15C3.58 9.21 3.38 9.23 3.19 9.2C3 9.17 2.81 9.1 2.65 8.99C2.49 8.88 2.35 8.74 2.25 8.57C2.15 8.4 2.09 8.21 2.07 8.02C2.07 8.01 2.07 8.01 2.07 8H2ZM2 11.25C2 11.05 2.04 10.86 2.12 10.68C2.2 10.5 2.32 10.34 2.46 10.21C2.61 10.08 2.78 9.98 2.96 9.92C3.15 9.86 3.35 9.84 3.54 9.87C3.73 9.9 3.92 9.97 4.08 10.08C4.24 10.19 4.38 10.33 4.48 10.5C4.58 10.67 4.64 10.86 4.66 11.05C4.68 11.25 4.65 11.44 4.58 11.62C4.51 11.81 4.4 11.97 4.26 12.11C4.12 12.24 3.95 12.34 3.77 12.4C3.58 12.46 3.38 12.48 3.19 12.45C3 12.42 2.81 12.35 2.65 12.24C2.49 12.13 2.35 11.99 2.25 11.82C2.15 11.65 2.09 11.46 2.07 11.27C2.07 11.26 2.07 11.26 2.07 11.25H2Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Numbered List"
                    onClick={() => applyFormatting("number")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2.5 12.5V11.5L3.5 11.25V8.5H2.5V7.5H4.5V11.25L5.5 11.5V12.5H2.5ZM3.5 5.5V2.5H2.5V1.5H4.5V5.5H3.5ZM6.5 4H14V5.5H6.5V4ZM6.5 7.25H14V8.75H6.5V7.25ZM6.5 10.5H14V12H6.5V10.5Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                </div>

                <div className="flex">
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Align Left"
                    onClick={() => applyFormatting("left")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2 4H14V5.5H2V4ZM2 7.25H10V8.75H2V7.25ZM2 10.5H14V12H2V10.5Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Align Center"
                    onClick={() => applyFormatting("center")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2 4H14V5.5H2V4ZM4 7.25H12V8.75H4V7.25ZM2 10.5H14V12H2V10.5Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                  <button
                    type="button"
                    className="rounded p-1.5 text-gray-800 hover:bg-gray-200"
                    title="Align Right"
                    onClick={() => applyFormatting("right")}
                  >
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M2 4H14V5.5H2V4ZM6 7.25H14V8.75H6V7.25ZM2 10.5H14V12H2V10.5Z"
                        fill="currentColor"
                      />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Textarea with increased height (50% taller) */}
              <textarea
                ref={textareaRef}
                className="h-36 w-full resize-none px-4 py-3 text-black transition-colors focus:border-0 focus:ring-0 focus:outline-none"
                placeholder="Add a message to accompany your shared analysis..."
                style={{ color: "black" }}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
            </div>
            <p className="mt-1 text-xs text-gray-600">
              Format your message using the toolbar above
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex justify-end space-x-3">
            <button
              className="rounded-md bg-gray-100 px-5 py-2 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              className={`flex min-w-[80px] items-center justify-center rounded px-4 py-2 transition-colors ${sendStatus === "idle" ? "bg-blue-600 text-white hover:bg-blue-700" : sendStatus === "sending" ? "cursor-wait bg-blue-500 text-white" : "bg-green-600 text-white"}`}
              onClick={handleShare}
              disabled={sendStatus !== "idle"}
            >
              {sendStatus === "idle" && "Send"}
              {sendStatus === "sending" && (
                <>
                  <svg
                    className="mr-2 -ml-1 h-4 w-4 animate-spin text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Sending...
                </>
              )}
              {sendStatus === "sent" && (
                <>
                  <svg
                    className="mr-2 -ml-1 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                  Sent!
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ShareModal
