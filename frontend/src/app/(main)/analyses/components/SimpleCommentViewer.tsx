import React, { useState } from "react"

import { Comment } from "./CommentViewer"

interface SimpleCommentViewerProps {
  comments: Comment[]
  onClose: () => void
  onAddResponse?: (commentId: string, response: string) => void
}

const SimpleCommentViewer: React.FC<SimpleCommentViewerProps> = ({
  comments,
  onClose,
  onAddResponse,
}) => {
  const [activeResponse, setActiveResponse] = useState<string | null>(null)
  const [responseText, setResponseText] = useState("")

  const handleSubmitResponse = (commentId: string) => {
    if (responseText.trim() && onAddResponse) {
      onAddResponse(commentId, responseText)
      setResponseText("")
      setActiveResponse(null)
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Overlay */}
      <div
        className="fixed inset-0 bg-black opacity-50"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div className="bg-primary z-50 mx-auto w-full max-w-md overflow-hidden rounded-lg shadow-lg">
        <div className="p-6">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-xl font-semibold text-white">Comments</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <svg
                className="h-5 w-5"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </button>
          </div>

          <div className="max-h-96 space-y-4 overflow-y-auto">
            {comments.length === 0 ? (
              <p className="py-4 text-center text-gray-400">No comments yet</p>
            ) : (
              comments.map((comment) => (
                <div key={comment.id} className="bg-primary rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <div className="bg-primary flex h-8 w-8 items-center justify-center rounded-full font-semibold text-white">
                        {comment.author.substring(0, 1).toUpperCase()}
                      </div>
                      <div className="ml-2">
                        <p className="font-medium text-white">
                          {comment.author}
                        </p>
                        <p className="text-xs text-gray-400">
                          {new Date(comment.timestamp).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    {comment.isRequest && (
                      <span className="rounded-full bg-amber-500 px-2 py-0.5 text-xs text-white">
                        Request
                      </span>
                    )}
                  </div>

                  <div className="mt-3">
                    <p className="text-gray-300">{comment.text}</p>

                    {comment.isRequest && comment.requestedFrom && (
                      <div className="mt-1 text-xs text-gray-400">
                        Requested from: {comment.requestedFrom.join(", ")}
                      </div>
                    )}
                  </div>

                  {comment.isRequest && onAddResponse && (
                    <div className="mt-3">
                      {activeResponse === comment.id ? (
                        <div className="mt-2 space-y-2">
                          <textarea
                            className="bg-primary focus:ring-primary w-full rounded border border-gray-600 p-2 text-white focus:ring-2 focus:outline-none"
                            rows={3}
                            placeholder="Type your response..."
                            value={responseText}
                            onChange={(e) => setResponseText(e.target.value)}
                          />
                          <div className="flex justify-end space-x-2">
                            <button
                              className="px-3 py-1 text-xs text-gray-300 hover:text-white"
                              onClick={() => setActiveResponse(null)}
                            >
                              Cancel
                            </button>
                            <button
                              className="bg-primary hover:bg-primary rounded px-3 py-1 text-xs text-white"
                              onClick={() => handleSubmitResponse(comment.id)}
                            >
                              Submit
                            </button>
                          </div>
                        </div>
                      ) : (
                        <button
                          className="text-primary hover:text-primary mt-2 text-sm font-medium"
                          onClick={() => setActiveResponse(comment.id)}
                        >
                          Add response
                        </button>
                      )}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SimpleCommentViewer
