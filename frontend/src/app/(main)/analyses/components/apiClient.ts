import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios"

import { API_CONFIG } from "./environment"

/**
 * Base API client for making HTTP requests
 */
class ApiClient {
  private client: AxiosInstance

  constructor(baseURL: string) {
    this.client = axios.create({
      baseURL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        "Content-Type": "application/json",
      },
    })

    // Add request interceptor for auth tokens, etc. if needed
    this.client.interceptors.request.use(
      (config) => {
        // Add auth token if available
        // const token = getAuthToken();
        // if (token) {
        //   config.headers.Authorization = `Bearer ${token}`;
        // }
        return config
      },
      (error) => Promise.reject(error)
    )

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle specific error cases
        if (error.response) {
          // Server responded with an error status
          console.error(
            "API Error:",
            error.response.status,
            error.response.data
          )

          // Handle specific status codes
          switch (error.response.status) {
            case 401:
              // Handle unauthorized
              break
            case 404:
              // Handle not found
              break
            default:
              // Handle other errors
              break
          }
        } else if (error.request) {
          // Request was made but no response received
          console.error("API Error: No response received", error.request)
        } else {
          // Error in setting up the request
          console.error("API Error:", error.message)
        }

        return Promise.reject(error)
      }
    )
  }

  /**
   * Make a GET request
   */
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.get(url, config)
    return response.data
  }

  /**
   * Make a POST request
   */
  async post<T>(
    url: string,
    data?: Record<string, unknown>,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response: AxiosResponse<T> = await this.client.post(url, data, config)
    return response.data
  }

  /**
   * Make a PUT request
   */
  async put<T>(
    url: string,
    data?: Record<string, unknown>,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response: AxiosResponse<T> = await this.client.put(url, data, config)
    return response.data
  }

  /**
   * Make a DELETE request
   */
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response: AxiosResponse<T> = await this.client.delete(url, config)
    return response.data
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient(API_CONFIG.BASE_URL)
