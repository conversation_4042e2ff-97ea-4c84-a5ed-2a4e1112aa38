import { AnalysisType, Chart } from "./types"

/**
 * Service for managing charts in the database
 */
export const chartService = {
  /**
   * Get charts for a specific analysis type
   */
  async getCharts(analysisType: AnalysisType): Promise<Chart[]> {
    try {
      const response = await fetch(`/api/charts?analysisType=${analysisType}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch charts: ${response.statusText}`)
      }

      const data = await response.json()
      return data.charts || []
    } catch (error) {
      console.error("Error fetching charts:", error)
      return []
    }
  },

  /**
   * Save all charts for a specific analysis type
   */
  async saveCharts(
    analysisType: AnalysisType,
    charts: Chart[]
  ): Promise<boolean> {
    try {
      const response = await fetch("/api/charts", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ analysisType, charts }),
      })

      if (!response.ok) {
        throw new Error(`Failed to save charts: ${response.statusText}`)
      }

      return true
    } catch (error) {
      console.error("Error saving charts:", error)
      return false
    }
  },

  /**
   * Update a single chart
   */
  async updateChart(
    analysisType: AnalysisType,
    chart: Chart
  ): Promise<boolean> {
    try {
      const response = await fetch("/api/charts", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ analysisType, chart }),
      })

      if (!response.ok) {
        throw new Error(`Failed to update chart: ${response.statusText}`)
      }

      return true
    } catch (error) {
      console.error("Error updating chart:", error)
      return false
    }
  },

  /**
   * Delete a chart
   */
  async deleteChart(
    analysisType: AnalysisType,
    chartId: string
  ): Promise<boolean> {
    try {
      const response = await fetch(
        `/api/charts?analysisType=${analysisType}&chartId=${chartId}`,
        {
          method: "DELETE",
        }
      )

      if (!response.ok) {
        throw new Error(`Failed to delete chart: ${response.statusText}`)
      }

      return true
    } catch (error) {
      console.error("Error deleting chart:", error)
      return false
    }
  },
}
