{"chartTypes": [{"id": "area", "name": "Area Chart", "description": "Shows how one or more values change over a continuous interval or time period", "icon": "area-chart", "allowedDimensions": ["date", "time", "category"], "allowedMeasures": ["percentage", "currency", "count", "number"], "recommendedFor": ["trends", "proportions", "distributions"], "rules": [{"rule": "requiresTimeDimension", "description": "Area charts work best with a time-based dimension on the x-axis", "severity": "warning"}, {"rule": "maxMeasures", "value": 3, "description": "For readability, limit to 3 measures", "severity": "warning"}]}, {"id": "bar", "name": "Bar Chart", "description": "Compares values across categories with rectangular bars", "icon": "bar-chart", "allowedDimensions": ["category", "geography", "date", "time"], "allowedMeasures": ["currency", "percentage", "count", "number"], "recommendedFor": ["comparisons", "rankings", "distributions"], "rules": [{"rule": "maxCategories", "value": 15, "description": "For readability, limit to 15 categories", "severity": "warning"}, {"rule": "maxMeasures", "value": 5, "description": "For readability, limit to 5 measures", "severity": "warning"}]}, {"id": "line", "name": "Line Chart", "description": "Shows trends over a continuous interval or time period", "icon": "line-chart", "allowedDimensions": ["date", "time", "number"], "allowedMeasures": ["currency", "percentage", "count", "number"], "recommendedFor": ["trends", "comparisons over time", "continuous data"], "rules": [{"rule": "requiresTimeDimension", "description": "Line charts work best with a time-based dimension on the x-axis", "severity": "warning"}, {"rule": "maxMeasures", "value": 5, "description": "For readability, limit to 5 lines", "severity": "warning"}]}, {"id": "pie", "name": "Pie Chart", "description": "Shows composition or proportion of categories as slices of a circle", "icon": "pie-chart", "allowedDimensions": ["category"], "allowedMeasures": ["percentage", "currency", "count", "number"], "recommendedFor": ["composition", "proportion", "part-to-whole relationships"], "rules": [{"rule": "maxCategories", "value": 7, "description": "For readability, limit to 7 categories", "severity": "error"}, {"rule": "singleMeasure", "description": "Pie charts can only display one measure", "severity": "error"}]}, {"id": "scatter", "name": "Scatter Plot", "description": "Shows the relationship between two measures", "icon": "scatter-chart", "allowedDimensions": ["category"], "allowedMeasures": ["currency", "percentage", "count", "number"], "recommendedFor": ["correlation", "distribution", "clusters"], "rules": [{"rule": "requiresTwoMeasures", "description": "Scatter plots require exactly two measures (x and y)", "severity": "error"}, {"rule": "optionalThirdMeasure", "description": "A third measure can be used for point size", "severity": "info"}]}, {"id": "heatmap", "name": "Heat Map", "description": "Shows values across two dimensions using color intensity", "icon": "heatmap-chart", "allowedDimensions": ["category", "date", "time", "geography"], "allowedMeasures": ["percentage", "currency", "count", "number"], "recommendedFor": ["correlation", "patterns", "outliers"], "rules": [{"rule": "requiresTwoDimensions", "description": "Heat maps require exactly two dimensions (x and y)", "severity": "error"}, {"rule": "singleMeasure", "description": "Heat maps can only display one measure (color intensity)", "severity": "error"}]}], "dimensionTypes": [{"id": "date", "name": "Date", "color": "blue", "examples": ["Year", "Quarter", "Month", "Day"]}, {"id": "time", "name": "Time", "color": "blue", "examples": ["Hour", "Minute", "Second"]}, {"id": "category", "name": "Category", "color": "purple", "examples": ["Product", "Department", "Status"]}, {"id": "geography", "name": "Geography", "color": "green", "examples": ["Country", "Region", "City", "ZIP"]}, {"id": "number", "name": "Number", "color": "orange", "examples": ["Age", "ID", "Rank"]}], "measureTypes": [{"id": "currency", "name": "<PERSON><PERSON><PERSON><PERSON>", "color": "yellow", "examples": ["Revenue", "Cost", "Profit"]}, {"id": "percentage", "name": "Percentage", "color": "yellow", "examples": ["<PERSON><PERSON>", "Growth Rate", "Conversion Rate"]}, {"id": "count", "name": "Count", "color": "red", "examples": ["Orders", "Customers", "Units"]}, {"id": "number", "name": "Number", "color": "red", "examples": ["Score", "Rating", "Index"]}]}