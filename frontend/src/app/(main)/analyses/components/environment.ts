/**
 * Environment configuration for the application
 */

// API configuration
export const API_CONFIG = {
  // Base URL for API requests
  BASE_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
  
  // Whether to use mock data instead of real API calls
  USE_MOCK_DATA: process.env.NEXT_PUBLIC_USE_MOCK_DATA === 'true' || true,
  
  // Timeout for API requests in milliseconds
  TIMEOUT: 10000,
  
  // Simulated delay for mock data in milliseconds
  MOCK_DELAY: 300,
};

// Application configuration
export const APP_CONFIG = {
  // Default currency
  DEFAULT_CURRENCY: 'usd',
  
  // Default time period
  DEFAULT_TIME_PERIOD: 'quarterly',
  
  // Default analysis type
  DEFAULT_ANALYSIS_TYPE: 'profitability',
};

// Theme configuration
export const THEME_CONFIG = {
  // Colors for filters
  FILTER_COLORS: ['#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
  
  // Colors for chart elements
  CHART_COLORS: {
    primary: '#4F46E5',
    secondary: '#10B981',
    tertiary: '#F59E0B',
    quaternary: '#EF4444',
    quinary: '#8B5CF6',
  },
  
  // Colors for financial indicators
  FINANCIAL_COLORS: {
    positive: '#10B981', // green
    negative: '#EF4444', // red
    neutral: '#6B7280', // gray
  },
};
