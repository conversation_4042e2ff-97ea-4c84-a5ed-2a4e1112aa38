import {
  AnalysisData,
  AnalysisView,
  DataSource,
  ExtendedRowData,
} from "./types"

// Mock data for analysis views
export const analysisViews: AnalysisView[] = [
  {
    id: 1,
    name: "Profitability Analysis",
    health: "green",
    children: [
      {
        id: 101,
        name: "Q1 2024 Profitability",
        parentId: 1,
        health: "green",
        createdAt: "2025-04-15T09:30:00",
        favorite: true,
      },
      {
        id: 102,
        name: "North America Profitability",
        parentId: 1,
        health: "amber",
        createdAt: "2025-04-22T14:45:00",
      },
      {
        id: 103,
        name: "Product Line A Analysis",
        parentId: 1,
        health: "green",
        createdAt: "2025-05-01T11:20:00",
        favorite: true,
      },
    ],
  },
  {
    id: 2,
    name: "Cash Flow Analysis",
    health: "green",
    children: [
      {
        id: 201,
        name: "Q1 2024 Cash Flow",
        parentId: 2,
        health: "green",
        createdAt: "2025-04-10T16:15:00",
        favorite: true,
      },
      {
        id: 202,
        name: "Working Capital Analysis",
        parentId: 2,
        health: "amber",
        createdAt: "2025-04-28T10:05:00",
      },
      {
        id: 203,
        name: "Cash Conversion Cycle",
        parentId: 2,
        health: "green",
        createdAt: "2025-05-05T13:45:00",
      },
    ],
  },
  {
    id: 3,
    name: "Revenue Analysis",
    health: "amber",
    children: [
      {
        id: 301,
        name: "Revenue by Region",
        parentId: 3,
        health: "green",
        createdAt: "2025-04-05T13:40:00",
      },
      {
        id: 302,
        name: "Revenue by Product",
        parentId: 3,
        health: "amber",
        createdAt: "2025-05-07T09:15:00",
      },
    ],
  },
  { id: 4, name: "Expense Analysis", health: "red" },
  { id: 5, name: "Balance Sheet Health", health: "green" },
  { id: 6, name: "Working Capital Management", health: "amber" },
  { id: 7, name: "Investment Analysis", health: "green" },
]

// Mock data for the narrative analysis
export const mockAnalysisData: AnalysisData = {
  title: "Profitability Analysis",
  summary:
    "Comprehensive view of company profitability metrics across different dimensions.",
  sections: [
    {
      id: "overview",
      title: "Executive Summary",
      content:
        "<p class='text-black'>Fiscal year 2024 key profitability highlights:</p>\n\n<ol class='list-decimal pl-5 space-y-2 text-black'>\n  <li>Gross profit margins increased by <span class='font-bold text-black border-b-4 border-green-600'>+2.3%</span> year-over-year</li>\n  <li>Primary drivers:\n    <ul class='list-disc pl-5 mt-1 space-y-1'>\n      <li>Operational efficiencies (unit production costs <span class='font-bold text-black border-b-4 border-green-600'>-5.2%</span>)</li>\n      <li>Strategic pricing adjustments (average selling price <span class='font-bold text-black border-b-4 border-green-600'>+3.1%</span>)</li>\n    </ul>\n  </li>\n  <li>EBITDA trending <span class='font-bold text-black border-b-4 border-green-600'>+5%</span> above industry average</li>\n  <li>Product line A remains strongest performer with <span class='font-bold text-black border-b-4 border-green-600'>42%</span> contribution margin</li>\n</ol>",
    },
    {
      id: "margin-analysis",
      title: "Margin Analysis",
      content:
        "<p class='text-black'>Our gross margin improvement can be attributed to three key factors:</p>\n\n<ol class='list-decimal pl-5 space-y-2 text-black'>\n  <li>Reduced raw material costs <span class='font-bold text-black border-b-4 border-red-600'>-12%</span> through strategic sourcing initiatives</li>\n  <li>Increased production efficiency <span class='font-bold text-black border-b-4 border-green-600'>+15%</span> from our plant modernization program</li>\n  <li>Favorable product mix shift toward higher-margin offerings</li>\n</ol>\n\n<p class='text-black mt-3'>The chart below illustrates the quarterly progression of our margin improvement.</p>",
    },
    {
      id: "product-performance",
      title: "Product Line Performance",
      content:
        "<p class='text-black'>Product line performance summary:</p>\n\n<ol class='list-decimal pl-5 space-y-2 text-black'>\n  <li>Product line A: Maintained position as highest-margin offering with contribution margins reaching <span class='font-bold text-black border-b-4 border-green-600'>42%</span> <span class='font-bold text-black border-b-4 border-green-600'>+3.5pp</span> over previous year</li>\n  <li>Product line B: Showed modest improvement of <span class='font-bold text-black border-b-4 border-green-600'>+1.2pp</span></li>\n  <li>Product line C: Smallest improvement at <span class='font-bold text-black border-b-4 border-green-600'>+0.8pp</span></li>\n</ol>\n\n<p class='text-black mt-3'>The relative performance of each product line is visualized below.</p>",
    },
    {
      id: "ebitda-analysis",
      title: "EBITDA Analysis",
      content:
        "<p class='text-black'>EBITDA performance highlights:</p>\n\n<ol class='list-decimal pl-5 space-y-2 text-black'>\n  <li>EBITDA growth to <span class='font-bold text-black border-b-4 border-green-600'>$24.3M</span> <span class='font-bold text-black border-b-4 border-green-600'>+15%</span> year-over-year</li>\n  <li>Performance <span class='font-bold text-black border-b-4 border-green-600'>+5%</span> above industry average</li>\n  <li>Improvement drivers:\n    <ul class='list-disc pl-5 mt-1 space-y-1'>\n      <li>Gross margin improvements as detailed above</li>\n      <li>Disciplined SG&A management (SG&A ratio reduced from <span class='font-bold text-black border-b-4 border-red-600'>22.1%</span> to <span class='font-bold text-black border-b-4 border-green-600'>20.3%</span>)</li>\n    </ul>\n  </li>\n  <li>EBITDA margin now at <span class='font-bold text-black border-b-4 border-green-600'>18.7%</span> (up from <span class='font-bold text-black border-b-4 border-red-600'>16.2%</span> in previous year)</li>\n</ol>",
    },
    {
      id: "outlook",
      title: "Outlook & Recommendations",
      content:
        "<p class='text-black'>Based on current trends and forward indicators, we project continued margin improvement in the coming fiscal year. To maximize profitability, we recommend:</p>\n\n<ol class='list-decimal pl-5 space-y-2 text-black'>\n  <li>Expand production capacity for Product line A (projected ROI: <span class='font-bold text-black border-b-4 border-green-600'>32%</span>)</li>\n  <li>Implement cost reduction program for Product line C (estimated savings of <span class='font-bold text-black border-b-4 border-green-600'>$1.2M</span>)</li>\n  <li>Evaluate price adjustments for Product line B to improve its contribution margin</li>\n  <li>Address rising logistics costs which increased by <span class='font-bold text-black border-b-4 border-red-600'>+8%</span> in Q4</li>\n</ol>",
    },
  ],
  data: {
    quarterlyResults: [
      {
        period: "Q1 2024",
        revenue: 5840000,
        cogs: 3212000,
        grossProfit: 2628000,
        margin: 45.0,
      },
      {
        period: "Q2 2024",
        revenue: 6120000,
        cogs: 3304800,
        grossProfit: 2815200,
        margin: 46.0,
      },
      {
        period: "Q3 2024",
        revenue: 6750000,
        cogs: 3577500,
        grossProfit: 3172500,
        margin: 47.0,
      },
      {
        period: "Q4 2024",
        revenue: 7320000,
        cogs: 3806400,
        grossProfit: 3513600,
        margin: 48.0,
      },
    ],
  },
  currentVersion: {
    id: "v1",
    isBase: true,
    createdAt: "2025-05-01T10:30:00",
  },
  versions: [
    {
      id: "v1",
      isBase: true,
      createdAt: "2025-05-01T10:30:00",
    },
    {
      id: "v2",
      isBase: false,
      createdAt: "2025-05-05T14:45:00",
    },
    {
      id: "v3",
      isBase: false,
      createdAt: "2025-05-08T09:15:00",
    },
  ],
}

// Initial data sources
export const initialDataSources: DataSource[] = [
  { id: 1, name: "Financial Database", color: "bg-blue-100 text-blue-800" },
  { id: 2, name: "CRM Data", color: "bg-green-100 text-green-800" },
  { id: 3, name: "ERP System", color: "bg-orange-100 text-orange-800" },
]

// Initial table data with extended information
export const initialTableData: ExtendedRowData[] = [
  // First set of data
  {
    period: "Q1 2024",
    revenue: 5840000,
    cogs: 3212000,
    grossProfit: 2628000,
    margin: 45.0,
    source: "Financial Database",
    unit: "All",
  },
  {
    period: "Q2 2024",
    revenue: 6120000,
    cogs: 3304800,
    grossProfit: 2815200,
    margin: 46.0,
    source: "Financial Database",
    unit: "All",
  },
  {
    period: "Q3 2024",
    revenue: 6750000,
    cogs: 3577500,
    grossProfit: 3172500,
    margin: 47.0,
    source: "Financial Database",
    unit: "All",
  },
  {
    period: "Q4 2024",
    revenue: 7320000,
    cogs: 3806400,
    grossProfit: 3513600,
    margin: 48.0,
    source: "Financial Database",
    unit: "All",
  },
  // Second set of data
  {
    period: "Q1 2024",
    revenue: 2840000,
    cogs: 1612000,
    grossProfit: 1228000,
    margin: 43.2,
    source: "CRM Data",
    unit: "North America",
  },
  {
    period: "Q2 2024",
    revenue: 3120000,
    cogs: 1704800,
    grossProfit: 1415200,
    margin: 45.4,
    source: "CRM Data",
    unit: "North America",
  },
  {
    period: "Q3 2024",
    revenue: 3750000,
    cogs: 1977500,
    grossProfit: 1772500,
    margin: 47.3,
    source: "CRM Data",
    unit: "North America",
  },
  {
    period: "Q4 2024",
    revenue: 4320000,
    cogs: 2306400,
    grossProfit: 2013600,
    margin: 46.6,
    source: "CRM Data",
    unit: "North America",
  },
  // Third set of data
  {
    period: "Q1 2024",
    revenue: 1840000,
    cogs: 1012000,
    grossProfit: 828000,
    margin: 45.0,
    source: "ERP System",
    unit: "Europe",
  },
  {
    period: "Q2 2024",
    revenue: 2120000,
    cogs: 1104800,
    grossProfit: 1015200,
    margin: 47.9,
    source: "ERP System",
    unit: "Europe",
  },
  {
    period: "Q3 2024",
    revenue: 2750000,
    cogs: 1477500,
    grossProfit: 1272500,
    margin: 46.3,
    source: "ERP System",
    unit: "Europe",
  },
  {
    period: "Q4 2024",
    revenue: 3320000,
    cogs: 1806400,
    grossProfit: 1513600,
    margin: 45.6,
    source: "ERP System",
    unit: "Europe",
  },
]
