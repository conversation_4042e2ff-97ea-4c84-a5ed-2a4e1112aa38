import { v4 as uuidv4 } from "uuid"

import { Chart, ChartType } from "./types"

// Helper function to create chart IDs
const createChartId = () => uuidv4()

// Default charts for profitability analysis
// These are ordered to create a logical narrative flow
export const defaultProfitabilityCharts: Chart[] = [
  // Chart 1: Revenue Trend
  {
    id: createChartId(),
    name: "Revenue Trend",
    type: "line",
    order: 1,
    dimensions: {
      x: {
        id: "period",
        name: "Period",
        field: "period",
        type: "time",
      },
      y: [
        {
          id: "revenue",
          name: "Revenue",
          field: "revenue",
          type: "measure",
          aggregation: "sum",
          format: "$0,0",
        },
      ],
    },
    settings: {
      showGrid: true,
      showLegend: false,
      showTrendline: true,
      title: "Revenue Trend Over Time",
      subtitle: "Quarterly revenue figures",
    },
    description:
      "Shows the overall revenue trend across periods, providing context for profitability analysis.",
  },

  // Chart 2: Gross Profit Margin
  {
    id: createChartId(),
    name: "Gross Profit Margin",
    type: "bar",
    order: 2,
    dimensions: {
      x: {
        id: "period",
        name: "Period",
        field: "period",
        type: "time",
      },
      y: [
        {
          id: "margin",
          name: "Gross Margin %",
          field: "margin",
          type: "measure",
          format: "0.0%",
        },
      ],
    },
    settings: {
      showGrid: true,
      showValues: true,
      title: "Gross Profit Margin",
      subtitle: "Percentage of revenue retained after direct costs",
    },
    description:
      "Illustrates gross profit margin performance, a key indicator of pricing strategy and cost control.",
  },

  // Chart 3: Revenue vs. Gross Profit
  {
    id: createChartId(),
    name: "Revenue vs. Gross Profit",
    type: "bar",
    order: 3,
    dimensions: {
      x: {
        id: "period",
        name: "Period",
        field: "period",
        type: "time",
      },
      y: [
        {
          id: "revenue",
          name: "Revenue",
          field: "revenue",
          type: "measure",
          aggregation: "sum",
          format: "$0,0",
        },
        {
          id: "grossProfit",
          name: "Gross Profit",
          field: "grossProfit",
          type: "measure",
          aggregation: "sum",
          format: "$0,0",
        },
      ],
    },
    settings: {
      showGrid: true,
      showLegend: true,
      title: "Revenue vs. Gross Profit",
      subtitle: "Comparison of top-line revenue and gross profit",
    },
    description:
      "Compares revenue and gross profit side by side, showing the relationship between sales and profitability.",
  },

  // Chart 4: Cost of Goods Sold Breakdown
  {
    id: createChartId(),
    name: "Cost of Goods Sold",
    type: "pie",
    order: 4,
    dimensions: {
      color: {
        id: "costCategory",
        name: "Cost Category",
        field: "costCategory",
        type: "category",
      },
      y: [
        {
          id: "value",
          name: "Amount",
          field: "value",
          type: "measure",
          aggregation: "sum",
          format: "$0,0",
        },
      ],
    },
    settings: {
      showLegend: true,
      showValues: true,
      title: "COGS Breakdown",
      subtitle: "Major components of cost of goods sold",
    },
    description:
      "Breaks down the components of COGS to identify major cost drivers affecting profitability.",
  },

  // Chart 5: Profitability by Business Unit
  {
    id: createChartId(),
    name: "Profitability by Business Unit",
    type: "bar",
    order: 5,
    dimensions: {
      x: {
        id: "unit",
        name: "Business Unit",
        field: "unit",
        type: "category",
      },
      y: [
        {
          id: "margin",
          name: "Profit Margin %",
          field: "margin",
          type: "measure",
          format: "0.0%",
        },
      ],
      color: {
        id: "unit",
        name: "Business Unit",
        field: "unit",
        type: "category",
      },
    },
    settings: {
      showGrid: true,
      showValues: true,
      title: "Profitability by Business Unit",
      subtitle: "Comparing profit margins across business units",
    },
    description:
      "Compares profitability across different business units to identify top and bottom performers.",
  },

  // Chart 6: Profitability Forecast
  {
    id: createChartId(),
    name: "Profitability Forecast",
    type: "line",
    order: 6,
    dimensions: {
      x: {
        id: "period",
        name: "Period",
        field: "period",
        type: "time",
      },
      y: [
        {
          id: "margin",
          name: "Projected Margin %",
          field: "projectedMargin",
          type: "measure",
          format: "0.0%",
        },
      ],
    },
    settings: {
      showGrid: true,
      showTrendline: true,
      title: "Profitability Forecast",
      subtitle: "Projected profit margins for upcoming periods",
    },
    description:
      "Projects future profitability based on current trends and forecasted business conditions.",
  },
]

// Function to get a copy of the default charts
export const getProfitabilityCharts = (): Chart[] => {
  return JSON.parse(JSON.stringify(defaultProfitabilityCharts))
}

// Function to add a new chart to the collection
export const addChart = (
  charts: Chart[],
  chartType: ChartType,
  name: string
): Chart[] => {
  const newChart: Chart = {
    id: createChartId(),
    name:
      name ||
      `New ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart`,
    type: chartType,
    order: charts.length + 1,
    dimensions: {
      x: {
        id: "period",
        name: "Period",
        field: "period",
        type: "time",
      },
      y: [
        {
          id: "value",
          name: "Value",
          field: "value",
          type: "measure",
          aggregation: "sum",
        },
      ],
    },
    settings: {
      showGrid: true,
      showLegend: true,
      title:
        name ||
        `New ${chartType.charAt(0).toUpperCase() + chartType.slice(1)} Chart`,
    },
  }

  return [...charts, newChart]
}

// Function to update a chart
export const updateChart = (charts: Chart[], updatedChart: Chart): Chart[] => {
  return charts.map((chart) =>
    chart.id === updatedChart.id ? updatedChart : chart
  )
}

// Function to delete a chart
export const deleteChart = (charts: Chart[], chartId: string): Chart[] => {
  const filteredCharts = charts.filter((chart) => chart.id !== chartId)

  // Reorder the remaining charts
  return filteredCharts.map((chart, index) => ({
    ...chart,
    order: index + 1,
  }))
}

// Function to reorder charts
export const reorderCharts = (
  charts: Chart[],
  startIndex: number,
  endIndex: number
): Chart[] => {
  const result = Array.from(charts)
  const [removed] = result.splice(startIndex, 1)
  result.splice(endIndex, 0, removed)

  // Update order property for all charts
  return result.map((chart, index) => ({
    ...chart,
    order: index + 1,
  }))
}
