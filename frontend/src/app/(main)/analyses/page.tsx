"use client"

import React, { use<PERSON>allback, useEffect, useRef, useState } from "react"

import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { cashFlowAnalysisData } from "@/app/(main)/analyses/components/cashFlowData"
import DeleteConfirmationModal from "@/app/(main)/analyses/components/DeleteConfirmationModal"
import EditAnalysisModal from "@/app/(main)/analyses/components/EditAnalysisModal"
import { Breadcrumb } from "@/contexts/breadcrumb"

import AnalysisSection from "./components/AnalysisSection"
import AnalyticsPanel from "./components/AnalyticsPanel"
import {
  deleteAnalysisView,
  editAnalysisView,
  fetchAnalysisData,
  fetchAnalysisViews,
  fetchDataSources,
  fetchTableData,
  toggleFavorite,
} from "./components/api"
import CashFlowAnalysis from "./components/CashFlowAnalysis"
import RawDataModal from "./components/RawDataModal"
import ShareModal from "./components/ShareModal"
import {
  AnalysisData,
  AnalysisFilter,
  AnalysisMeasure,
  AnalysisType,
  AnalysisView,
  Currency,
  DataSource,
  ExtendedRowData,
  Filter,
  TimePeriod,
} from "./components/types"

const Analyses = () => {
  // State for UI visibility
  const [analyticsPanelVisible, setAnalyticsPanelVisible] = useState(true)
  const [rawDataModalVisible, setRawDataModalVisible] = useState(false)
  const [shareModalVisible, setShareModalVisible] = useState(false)

  // Ref for export button (used for PDF export popup)
  const exportButtonRef = useRef<HTMLButtonElement>(null)

  // State for filters and analysis settings
  const [activeFilters, setActiveFilters] = useState<Filter[]>([
    { id: 1, value: "North America", color: "#4F46E5", dataSourceId: 1 },
    { id: 2, value: "Q1-Q4 2024", color: "#10B981", dataSourceId: 2 },
  ])
  const [selectedAnalysisType, setSelectedAnalysisType] =
    useState<AnalysisType>("profitability")
  const [selectedTimePeriod, setSelectedTimePeriod] =
    useState<TimePeriod>("quarterly")
  const [selectedYear, setSelectedYear] = useState("2024")
  const [selectedCurrency, setSelectedCurrency] = useState<Currency>("usd")

  // State for API data
  const [analysisViews, setAnalysisViews] = useState<AnalysisView[]>([])
  const [activeViewId, setActiveViewId] = useState<number>(1)
  const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null)
  const [dataSources, setDataSources] = useState<DataSource[]>([])

  // State for table data with infinite scroll
  const [tableData, setTableData] = useState<ExtendedRowData[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)

  // Fetch data from the API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)

        // Fetch analysis views
        const views = await fetchAnalysisViews()
        setAnalysisViews(views)

        if (views.length > 0) {
          const firstViewId = views[0].id
          setActiveViewId(firstViewId)

          // Fetch data for the first view
          const data = await fetchAnalysisData(firstViewId)
          setAnalysisData(data)
        }

        // Fetch table data and data sources
        const tableData = await fetchTableData()
        setTableData(tableData)

        const sources = await fetchDataSources()
        setDataSources(sources)
      } catch (error) {
        console.error("Error fetching data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  // Function to load more data
  const loadMoreData = useCallback(() => {
    if (isLoading) return

    setIsLoading(true)

    // In a real implementation, you would fetch more data from the API
    // For now, we'll just set hasMore to false
    setTimeout(() => {
      setHasMore(false)
      setIsLoading(false)
    }, 800)
  }, [isLoading])

  // Event handlers
  const handleFilterRemove = useCallback((filterId: number) => {
    setActiveFilters((prev) => prev.filter((filter) => filter.id !== filterId))
  }, [])

  const handleFilterAdd = useCallback(
    (filter: Omit<Filter, "id">, dataSourceId: number) => {
      setActiveFilters((prev) => {
        const newId = Math.max(...prev.map((f) => f.id), 0) + 1
        return [...prev, { ...filter, id: newId, dataSourceId }]
      })
    },
    []
  )

  const handleViewRawData = useCallback(() => {
    setRawDataModalVisible(true)
  }, [])

  const handleApplyChanges = useCallback(() => {
    console.log("Applied settings")
    // Here you would typically make an API call to apply the changes
    // For now, we'll just update the local state
  }, [])

  const handleSaveAnalysis = useCallback(
    (parentId: number, name: string) => {
      console.log("Saving analysis", { parentId, name })
      // Here you would typically make an API call to save the analysis
      // For now, we'll just update the local state with a mock implementation
      const parentView = analysisViews.find((view) => view.id === parentId)
      if (parentView) {
        const newId = Math.max(...analysisViews.map((view) => view.id)) + 1
        // Convert Filter[] to AnalysisFilter[]
        const analysisFilters: AnalysisFilter[] = activeFilters.map(
          (filter) => ({
            id: filter.id.toString(),
            field: "region", // Default field, would be dynamic in real app
            operator: "equals" as const,
            value: filter.value,
            label: filter.value,
          })
        )

        // Convert analysis settings to AnalysisMeasure[]
        const analysisMeasures: AnalysisMeasure[] = [
          {
            id: "analysis_type",
            field: "type",
            aggregation: "count" as const,
            label: selectedAnalysisType,
          },
          {
            id: "currency",
            field: "currency",
            aggregation: "count" as const,
            label: selectedCurrency,
          },
        ]

        const newView: AnalysisView = {
          id: newId,
          name,
          parentId,
          health: "green",
          filters: analysisFilters,
          measures: analysisMeasures,
        }

        // Add the new view as a child of the parent view
        const updatedViews = analysisViews.map((view) => {
          if (view.id === parentId) {
            return {
              ...view,
              children: [...(view.children || []), newView],
            }
          }
          return view
        })

        setAnalysisViews([...updatedViews, newView])
        setActiveViewId(newId)
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      analysisViews,
      activeFilters,
      selectedAnalysisType,
      selectedCurrency,
      selectedTimePeriod,
      selectedYear,
    ]
  )

  const handleDrillDown = useCallback(() => {
    console.log("Drilling down into data")
    // Here you would typically show more detailed data or navigate to a detail view
  }, [])

  // State for views with favorites sorted to the top
  const [sortedViews, setSortedViews] = useState<AnalysisView[]>(analysisViews)
  // Track which parent items are hovered
  const [hoveredItem, setHoveredItem] = useState<number | null>(null)

  // State for modals
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [selectedAnalysis, setSelectedAnalysis] = useState<{
    id: number
    name: string
  } | null>(null)

  // Sort views whenever analysisViews changes
  useEffect(() => {
    // Sort the views to put favorites at the top
    const sortViews = (views: AnalysisView[]): AnalysisView[] => {
      return [...views].map((view) => {
        if (view.children && view.children.length > 0) {
          // Sort children with favorites at the top
          const sortedChildren = [...view.children].sort((a, b) => {
            if (a.favorite && !b.favorite) return -1
            if (!a.favorite && b.favorite) return 1
            return 0
          })
          return { ...view, children: sortedChildren }
        }
        return view
      })
    }

    setSortedViews(sortViews(analysisViews))
  }, [analysisViews])

  // Function to handle mouse enter on parent item
  const handleMouseEnter = (viewId: number) => {
    if (hoveredItem !== viewId) {
      setHoveredItem(viewId)
    }
  }

  // Function to handle mouse leave
  const handleMouseLeave = () => {
    setHoveredItem(null)
  }

  // Function to toggle favorite status
  const handleToggleFavorite = async (e: React.MouseEvent, childId: number) => {
    e.stopPropagation() // Prevent triggering the parent button click

    try {
      const result = await toggleFavorite(childId)
      if (result.success) {
        // The API service already updates the mock data, so we just need to re-sort
        setSortedViews((prev) => {
          return prev.map((view) => {
            if (view.children && view.children.length > 0) {
              // Sort children with favorites at the top
              const sortedChildren = [...view.children].sort((a, b) => {
                if (a.favorite && !b.favorite) return -1
                if (!a.favorite && b.favorite) return 1
                return 0
              })
              return { ...view, children: sortedChildren }
            }
            return view
          })
        })
      }
    } catch (error) {
      console.error("Error toggling favorite:", error)
    }
  }

  // Function to open edit modal
  const handleEditClick = (e: React.MouseEvent, child: AnalysisView) => {
    e.stopPropagation() // Prevent triggering the parent button click
    setSelectedAnalysis({ id: child.id, name: child.name })
    setEditModalOpen(true)
  }

  // Function to open delete confirmation modal
  const handleDeleteClick = (e: React.MouseEvent, child: AnalysisView) => {
    e.stopPropagation() // Prevent triggering the parent button click
    e.preventDefault() // Prevent any default browser behavior

    // Set the selected analysis and open the modal
    setSelectedAnalysis({ id: child.id, name: child.name })
    setDeleteModalOpen(true)
  }

  const onViewSelect = useCallback((viewId: number) => {
    setActiveViewId(viewId)

    // Check if this is the Cash Flow Analysis view
    const selectedView = analysisViews.find((view) => view.id === viewId)
    if (selectedView && selectedView.name === "Cash Flow Analysis") {
      setSelectedAnalysisType("cashflow")
    } else {
      setSelectedAnalysisType("profitability")
    }

    // For now, we'll just set the analysis data based on the view ID
    // In a real implementation, you would fetch the data from the API
    if (viewId === 2) {
      // Cash Flow Analysis
      setAnalysisData(cashFlowAnalysisData)
      setSelectedAnalysisType("cashflow")
    } else {
      // Default to Profitability Analysis
      fetchAnalysisData(viewId).then((data) => {
        setAnalysisData(data)
        setSelectedAnalysisType("profitability")
      })
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Function to save edited analysis
  const handleSaveEdit = async (newName: string) => {
    if (selectedAnalysis) {
      try {
        const result = await editAnalysisView(selectedAnalysis.id, {
          name: newName,
        })
        if (result.success) {
          // Close the modal
          setEditModalOpen(false)
          setSelectedAnalysis(null)
        }
      } catch (error) {
        console.error("Error editing analysis:", error)
      }
    }
  }

  // Function to confirm deletion
  const handleConfirmDelete = async () => {
    if (selectedAnalysis) {
      try {
        const result = await deleteAnalysisView(selectedAnalysis.id)
        if (result.success) {
          // Close the modal
          setDeleteModalOpen(false)
          setSelectedAnalysis(null)

          // Update the UI to reflect the deletion
          setSortedViews((prev) => {
            return prev.map((view) => {
              if (view.children && view.children.length > 0) {
                // Filter out the deleted child
                const updatedChildren = view.children.filter(
                  (child) => child.id !== selectedAnalysis.id
                )
                return { ...view, children: updatedChildren }
              }
              return view
            })
          })
        }
      } catch (error) {
        console.error("Error deleting analysis:", error)
      }
    }
  }

  // Function to close the delete modal
  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setSelectedAnalysis(null)
  }

  return (
    <>
      <Breadcrumb links={[{ label: "Analyses" }]} />

      <UnderConstructionAlert />

      {/* Delete Confirmation Modal - Rendered at the top level */}
      <DeleteConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
        itemName={selectedAnalysis?.name || ""}
      />

      {/* Edit Analysis Modal */}
      <EditAnalysisModal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        onSave={handleSaveEdit}
        initialName={selectedAnalysis?.name || ""}
      />

      <main className="leviathan-content relative -m-4 flex flex-grow flex-col overflow-hidden">
        {/* Analytics panel drawer */}
        <AnalyticsPanel
          visible={analyticsPanelVisible}
          onClose={() => setAnalyticsPanelVisible(false)}
          onRawDataView={handleViewRawData}
          dataSources={dataSources}
          activeFilters={activeFilters}
          selectedAnalysisType={selectedAnalysisType}
          selectedCurrency={selectedCurrency}
          selectedTimePeriod={selectedTimePeriod}
          selectedYear={selectedYear}
          activeViewId={activeViewId}
          analysisName={analysisData?.title || "Analysis"}
          onAnalysisTypeChange={setSelectedAnalysisType}
          onCurrencyChange={setSelectedCurrency}
          onTimePeriodChange={setSelectedTimePeriod}
          onYearChange={setSelectedYear}
          onFilterRemove={handleFilterRemove}
          onFilterAdd={handleFilterAdd}
          onApplyChanges={handleApplyChanges}
          onSaveAnalysis={handleSaveAnalysis}
        />

        {/* Toggle button for analytics panel - positioned at middle-left of analysis control panel */}
        <button
          className="fixed top-1/2 right-0 z-20 -translate-y-1/2 transform rounded-l-md border border-r-0 border-gray-200 bg-white p-2 text-gray-500 shadow-md hover:text-gray-700"
          onClick={() => setAnalyticsPanelVisible(!analyticsPanelVisible)}
          style={{ marginRight: analyticsPanelVisible ? "32rem" : "0" }}
        >
          {analyticsPanelVisible ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          )}
        </button>

        <div className="flex items-start gap-8 p-8">
          <div className="bg-sidebar min-w-[220px] py-4">
            <h2 className="text-primary mb-1 px-4 text-xs font-medium">
              Analysis Views
            </h2>
            <ul>
              {sortedViews.map((view) => (
                <li key={view.id}>
                  {/* Parent item container */}
                  <div
                    className="relative"
                    onMouseEnter={() =>
                      view.children &&
                      view.children.length > 0 &&
                      handleMouseEnter(view.id)
                    }
                  >
                    {/* Parent item */}
                    <div className="flex w-full">
                      {/* Indicator for items with children */}
                      <div className="flex items-center pl-2">
                        {view.children && view.children.length > 0 ? (
                          <div className="flex-shrink-0 p-1 text-gray-400">
                            {/* Removed speech bubble icon as requested */}
                            <div className="h-3 w-3"></div>
                          </div>
                        ) : (
                          <div className="w-5"></div>
                        )}
                      </div>

                      {/* Parent item button */}
                      <button
                        className={`leviathan-analysis-item flex flex-grow items-center px-2 py-2 text-left ${view.id === activeViewId ? "active" : ""}`}
                        onClick={() => onViewSelect(view.id)}
                      >
                        <div className="flex items-center">
                          {view.health && (
                            <span
                              className={`mr-2 h-2 w-2 rounded-full opacity-80`}
                              style={{
                                backgroundColor:
                                  view.health === "green"
                                    ? "#22c55e"
                                    : view.health === "amber"
                                      ? "#f97316"
                                      : "#ef4444",
                              }}
                            ></span>
                          )}
                          <span className="text-sm">{view.name}</span>
                          {view.children && view.children.length > 0 && (
                            <span className="text-muted-foreground ml-2 text-xs">
                              {view.children.length}
                            </span>
                          )}
                        </div>
                      </button>
                    </div>

                    {/* Invisible bridge element to prevent dropdown from closing */}
                    {view.children &&
                      view.children.length > 0 &&
                      hoveredItem === view.id && (
                        <div
                          className="fixed z-[99]"
                          style={{
                            left: "240px",
                            top: "0",
                            width: "20px",
                            height: "100%",
                            pointerEvents: "auto",
                          }}
                          onMouseEnter={() => handleMouseEnter(view.id)}
                        />
                      )}

                    {/* Dropdown menu for child items */}
                    {view.children &&
                      view.children.length > 0 &&
                      hoveredItem === view.id && (
                        <div
                          className="bg-sidebar absolute z-[100] min-w-[280px] rounded-md py-2 shadow-lg"
                          style={{
                            left: "240px",
                            top: "auto",
                            transform: "translateY(-20px)", // Position it slightly higher for better alignment
                          }}
                          onMouseEnter={() => handleMouseEnter(view.id)}
                          onMouseLeave={handleMouseLeave}
                        >
                          <div className="border-opacity-30 border-primary text-primary mb-1 border-b px-3 py-1 text-xs tracking-wider uppercase">
                            Saved Analyses
                          </div>
                          <ul className="space-y-2">
                            {" "}
                            {/* Added vertical spacing between list items */}
                            {view.children.map((child) => (
                              <li key={child.id}>
                                <div
                                  className={`relative w-full px-3 py-3 text-left ${child.id === activeViewId ? "bg-primary text-primary-foreground" : "text-foreground"}`}
                                >
                                  <div className="flex items-start justify-between">
                                    {" "}
                                    {/* Changed to items-start for better alignment */}
                                    <button
                                      className="flex flex-grow flex-col text-left"
                                      onClick={() => onViewSelect(child.id)}
                                    >
                                      <span className="block text-left text-sm font-medium">
                                        {child.name}
                                      </span>
                                      {child.createdAt && (
                                        <span className="mt-2 block text-left text-xs text-gray-400">
                                          {" "}
                                          {/* Increased top margin */}
                                          Created:{" "}
                                          {new Date(
                                            child.createdAt
                                          ).toLocaleDateString("en-US", {
                                            year: "numeric",
                                            month: "short",
                                            day: "numeric",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                          })}
                                        </span>
                                      )}
                                    </button>
                                    <div className="ml-2 flex flex-shrink-0 items-center space-x-1">
                                      {/* Favorite Star Icon - Enhanced hover state */}
                                      <button
                                        className={`rounded p-1.5 text-sm transition-colors duration-150 ${
                                          child.favorite
                                            ? "hover:bg-opacity-30 text-yellow-400 hover:bg-yellow-900"
                                            : "text-gray-500 hover:bg-[var(--navy-lighter)] hover:text-yellow-300"
                                        }`}
                                        onClick={(e) =>
                                          handleToggleFavorite(e, child.id)
                                        }
                                        title={
                                          child.favorite
                                            ? "Remove from favorites"
                                            : "Add to favorites"
                                        }
                                      >
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-4 w-4"
                                          fill={
                                            child.favorite
                                              ? "currentColor"
                                              : "none"
                                          }
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={1.5}
                                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                                          />
                                        </svg>
                                      </button>

                                      {/* Edit Icon - Enhanced hover state */}
                                      <button
                                        className="hover:bg-opacity-30 rounded p-1.5 text-sm text-gray-400 transition-colors duration-150 hover:bg-blue-700 hover:text-white"
                                        onClick={(e) =>
                                          handleEditClick(e, child)
                                        }
                                        title="Edit analysis"
                                      >
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-4 w-4"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={1.5}
                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                                          />
                                        </svg>
                                      </button>

                                      {/* Delete Icon - Enhanced hover state */}
                                      <button
                                        className="hover:bg-opacity-30 rounded p-1.5 text-sm text-gray-400 transition-colors duration-150 hover:bg-red-900 hover:text-red-300"
                                        onClick={(e) =>
                                          handleDeleteClick(e, child)
                                        }
                                        title="Delete analysis"
                                      >
                                        <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          className="h-4 w-4"
                                          fill="none"
                                          viewBox="0 0 24 24"
                                          stroke="currentColor"
                                        >
                                          <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={1.5}
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                                          />
                                        </svg>
                                      </button>
                                    </div>
                                  </div>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                  </div>
                </li>
              ))}
            </ul>
          </div>

          {/* Content area with narrative flow */}
          <div className="leviathan-document w-full flex-grow overflow-y-auto">
            {isLoading ? (
              <div className="flex h-full items-center justify-center">
                <p className="text-lg">Loading...</p>
              </div>
            ) : analysisData ? (
              <>
                {/* Document title aligned with content */}
                <div className="mb-2 flex items-center justify-between">
                  <div>
                    <h1 className="border-primary text-primary dark:text-primary text-4xl font-semibold">
                      {selectedAnalysisType === "cashflow"
                        ? "Cash Flow Analysis"
                        : analysisData.title}
                    </h1>
                    <div className="mt-1 mb-4 flex items-center">
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Template: </span>
                        <span className="text-primary font-semibold">
                          {analysisData.currentVersion.isBase
                            ? "BASE"
                            : "Snapshot"}
                        </span>
                      </div>
                      <div className="mx-2 text-gray-400">•</div>
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">Created: </span>
                        <span>
                          {new Date(
                            analysisData.currentVersion.createdAt
                          ).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <button
                      className="rounded-full bg-gray-100 p-2 text-gray-600 transition-colors hover:bg-gray-200 hover:text-gray-800"
                      title="Take Snapshot"
                      onClick={() => {
                        // Create a new snapshot
                        const newSnapshot = {
                          id: `v${analysisData.versions ? analysisData.versions.length + 1 : 1}`,
                          isBase: false,
                          createdAt: new Date().toISOString(),
                        }

                        // Update the analysis data with the new snapshot
                        const updatedVersions = [
                          ...(analysisData.versions || []),
                          newSnapshot,
                        ]
                        setAnalysisData({
                          ...analysisData,
                          currentVersion: newSnapshot,
                          versions: updatedVersions,
                        })

                        console.log("Created new snapshot:", newSnapshot)
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                      </svg>
                    </button>
                    <button
                      ref={exportButtonRef}
                      className="rounded-full bg-gray-100 p-2 text-gray-600 transition-colors hover:bg-gray-200 hover:text-gray-800"
                      title="Export to PDF"
                      onClick={() => {
                        // Show browser's print dialog
                        const printWindow = window.open("", "_blank")
                        if (printWindow) {
                          printWindow.document.write(`
                          <html>
                            <head>
                              <title>${analysisData.title} - Export</title>
                              <style>
                                body { font-family: Arial, sans-serif; padding: 20px; }
                                h1 { color: #1e40af; }
                                h2 { color: #1e40af; border-bottom: 1px solid #e5e7eb; padding-bottom: 8px; }
                                p { color: #4b5563; line-height: 1.5; }
                              </style>
                            </head>
                            <body>
                              <h1>${analysisData.title}</h1>
                              ${analysisData.sections
                                .map(
                                  (section) => `
                                <h2>${section.title}</h2>
                                <p>${section.content}</p>
                              `
                                )
                                .join("")}
                            </body>
                          </html>
                        `)
                          printWindow.document.close()
                          printWindow.print()
                          printWindow.onafterprint = () => printWindow.close()
                        }
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                        />
                      </svg>
                    </button>
                    <button
                      className="rounded-full bg-gray-100 p-2 text-gray-600 transition-colors hover:bg-gray-200 hover:text-gray-800"
                      title="Share"
                      onClick={() => setShareModalVisible(true)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                        />
                      </svg>
                    </button>
                  </div>
                </div>

                {/* Render appropriate analysis component based on type */}
                {selectedAnalysisType === "cashflow" ? (
                  <CashFlowAnalysis />
                ) : (
                  // Render Profitability Analysis sections
                  analysisData.sections.map((section, index) => (
                    <AnalysisSection
                      key={section.id}
                      section={section}
                      data={
                        index === 1
                          ? analysisData.data.quarterlyResults
                          : undefined
                      }
                      onDrillDown={handleDrillDown}
                      onRawDataView={
                        index === 1 ? handleViewRawData : undefined
                      }
                      showDataTable={index === 1}
                    />
                  ))
                )}
              </>
            ) : (
              <div className="flex h-full items-center justify-center">
                <p className="text-lg">No analysis data available</p>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Raw data modal */}
      <RawDataModal
        visible={rawDataModalVisible}
        onClose={() => setRawDataModalVisible(false)}
        tableData={tableData}
        activeFilters={activeFilters}
        isLoading={isLoading}
        hasMore={hasMore}
        onLoadMore={loadMoreData}
        onFilterRemove={handleFilterRemove}
      />

      {/* Share modal */}
      {analysisData && (
        <ShareModal
          visible={shareModalVisible}
          onClose={() => setShareModalVisible(false)}
          analysisTitle={analysisData.title}
          templateType={
            analysisData.currentVersion.isBase ? "BASE" : "Snapshot"
          }
          createdAt={analysisData.currentVersion.createdAt}
        />
      )}
    </>
  )
}

export default Analyses
