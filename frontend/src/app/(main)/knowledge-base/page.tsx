"use client"

import React, { useEffect, useState } from "react"
import {
  Download,
  Eye,
  File,
  FileCode,
  FileSpreadsheet,
  FileText,
  ImageIcon,
  Plus,
  Search,
  Trash2,
  Upload,
  X,
} from "lucide-react"

import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

type DocumentStatus = "ingested" | "warning" | "error"

// Extended document interface
interface Document {
  id: string
  name: string
  type: string
  size: string
  lastModified: string
  source: "local" | "s3" | "google-drive"
  addedBy: string
  status: DocumentStatus
}

const mockDocuments: Document[] = [
  {
    id: "1",
    name: "Q1 Financial Report.pdf",
    type: "pdf",
    size: "2.4 MB",
    lastModified: "May 8, 2025",
    source: "local",
    addedBy: "<PERSON>",
    status: "ingested",
  },
  {
    id: "2",
    name: "Marketing Strategy.docx",
    type: "docx",
    size: "1.8 MB",
    lastModified: "May 5, 2025",
    source: "local",
    addedBy: "<PERSON>",
    status: "ingested",
  },
  {
    id: "3",
    name: "Sales Data 2025.xlsx",
    type: "xlsx",
    size: "3.2 MB",
    lastModified: "May 3, 2025",
    source: "s3",
    addedBy: "Alex Rodriguez",
    status: "warning",
  },
  {
    id: "4",
    name: "Product Roadmap.pptx",
    type: "pptx",
    size: "5.7 MB",
    lastModified: "April 28, 2025",
    source: "google-drive",
    addedBy: "Jamie Williams",
    status: "ingested",
  },
  {
    id: "5",
    name: "Customer Feedback Summary.csv",
    type: "csv",
    size: "1.1 MB",
    lastModified: "April 25, 2025",
    source: "s3",
    addedBy: "Taylor Smith",
    status: "error",
  },
  {
    id: "6",
    name: "Brand Guidelines.pdf",
    type: "pdf",
    size: "8.3 MB",
    lastModified: "April 20, 2025",
    source: "google-drive",
    addedBy: "Jordan Lee",
    status: "ingested",
  },
  {
    id: "7",
    name: "Project Timeline.xlsx",
    type: "xlsx",
    size: "1.5 MB",
    lastModified: "April 18, 2025",
    source: "local",
    addedBy: "Casey Morgan",
    status: "warning",
  },
  {
    id: "8",
    name: "Competitor Analysis.docx",
    type: "docx",
    size: "2.2 MB",
    lastModified: "April 15, 2025",
    source: "s3",
    addedBy: "Riley Thompson",
    status: "ingested",
  },
  {
    id: "9",
    name: "User Research Interviews.pdf",
    type: "pdf",
    size: "3.7 MB",
    lastModified: "May 10, 2025",
    source: "local",
    addedBy: "Morgan Davis",
    status: "ingested",
  },
  {
    id: "10",
    name: "Budget Forecast 2025-2026.xlsx",
    type: "xlsx",
    size: "1.9 MB",
    lastModified: "May 9, 2025",
    source: "s3",
    addedBy: "Pat Wilson",
    status: "error",
  },
  {
    id: "11",
    name: "Product Launch Plan.pptx",
    type: "pptx",
    size: "4.2 MB",
    lastModified: "May 7, 2025",
    source: "google-drive",
    addedBy: "Sam Taylor",
    status: "ingested",
  },
  {
    id: "12",
    name: "API Documentation.md",
    type: "md",
    size: "0.8 MB",
    lastModified: "May 6, 2025",
    source: "local",
    addedBy: "Alex Johnson",
    status: "warning",
  },
]

const KnowledgeBase = () => {
  const [documents, setDocuments] = useState(mockDocuments)
  const [filteredDocuments, setFilteredDocuments] = useState(mockDocuments)
  const [selectedTypes, setSelectedTypes] = useState<string[]>([])
  const [selectedSources, setSelectedSources] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showUploader, setShowUploader] = useState(false)

  // Filter documents based on selected filters and search query
  useEffect(() => {
    let filtered = [...documents]

    // Filter by file type
    if (selectedTypes.length > 0) {
      filtered = filtered.filter((doc) => {
        const type = doc.type.toLowerCase()
        return (
          (selectedTypes.includes("document") &&
            (type.includes("pdf") ||
              type.includes("doc") ||
              type.includes("txt"))) ||
          (selectedTypes.includes("spreadsheet") &&
            (type.includes("xls") || type.includes("csv"))) ||
          (selectedTypes.includes("presentation") && type.includes("ppt")) ||
          (selectedTypes.includes("image") &&
            (type.includes("jpg") ||
              type.includes("png") ||
              type.includes("gif"))) ||
          (selectedTypes.includes("code") &&
            (type.includes("js") ||
              type.includes("py") ||
              type.includes("java") ||
              type.includes("md")))
        )
      })
    }

    // Filter by source
    if (selectedSources.length > 0) {
      filtered = filtered.filter((doc) => selectedSources.includes(doc.source))
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (doc) =>
          doc.name.toLowerCase().includes(query) ||
          doc.type.toLowerCase().includes(query) ||
          doc.addedBy.toLowerCase().includes(query)
      )
    }

    setFilteredDocuments(filtered)
  }, [documents, selectedTypes, selectedSources, searchQuery])

  // Handle file upload
  const handleUpload = (files: File[]) => {
    // In a real app, you would upload these files to your backend
    // For this demo, we'll just add them to our local state
    const newDocuments = files.map((file, index) => ({
      id: `new-${Date.now()}-${index}`,
      name: file.name,
      type: file.name.split(".").pop() || "unknown",
      size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
      lastModified: new Date().toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: "numeric",
      }),
      source: "local" as const,
      addedBy: "Current User",
      status: "ingested" as DocumentStatus,
    }))

    setDocuments((prev) => [...newDocuments, ...prev])
    setShowUploader(false)
  }

  // Helper function to get the appropriate icon for a file type
  const getFileIcon = (fileType: string) => {
    const type = fileType.toLowerCase()
    if (type === "pdf" || type === "doc" || type === "docx" || type === "txt") {
      return <FileText size={18} className="text-blue-500" />
    } else if (type === "xls" || type === "xlsx" || type === "csv") {
      return <FileSpreadsheet size={18} className="text-green-500" />
    } else if (type === "jpg" || type === "png" || type === "gif") {
      return <ImageIcon size={18} className="text-purple-500" />
    } else if (
      type === "js" ||
      type === "py" ||
      type === "java" ||
      type === "md"
    ) {
      return <FileCode size={18} className="text-yellow-500" />
    } else {
      return <File size={18} className="text-gray-500" />
    }
  }

  // Helper function to get the appropriate badge for a source
  const getSourceBadge = (source: string) => {
    if (source === "local") {
      return (
        <span className="rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
          Local
        </span>
      )
    } else if (source === "s3") {
      return (
        <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
          S3
        </span>
      )
    } else {
      return (
        <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
          Google Drive
        </span>
      )
    }
  }

  // Helper function to get the appropriate badge for a status
  const getStatusBadge = (status: DocumentStatus) => {
    if (status === "ingested") {
      return (
        <span className="rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">
          Ingested
        </span>
      )
    } else if (status === "warning") {
      return (
        <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
          Warning
        </span>
      )
    } else {
      return (
        <span className="rounded-full bg-red-100 px-2 py-1 text-xs font-medium text-red-800">
          Error
        </span>
      )
    }
  }
  return (
    <>
      <Breadcrumb links={[{ label: "KnowledgeBase" }]} />

      <UnderConstructionAlert />

      <main className="leviathan-content relative flex flex-grow flex-col overflow-auto">
        <div>
          <div className="mb-6 flex items-center justify-between">
            <h1 className="text-primary text-2xl font-semibold">
              Knowledge Base
            </h1>
            <button
              onClick={() => setShowUploader(true)}
              className="bg-primary hover:bg-primary/90 flex items-center rounded-md px-4 py-2 text-sm font-medium text-white"
            >
              <Plus size={16} className="mr-1" />
              Add Documents
            </button>
          </div>

          {/* Search bar and filters */}
          <div className="mb-6 rounded-lg bg-white p-4 shadow-sm">
            <div className="flex items-center gap-4">
              <div className="relative flex-grow">
                <Search
                  size={18}
                  className="absolute top-1/2 left-3 -translate-y-1/2 transform text-gray-400"
                />
                <input
                  type="text"
                  placeholder="Search documents..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="focus:ring-primary w-full rounded-md border border-gray-300 py-2 pr-4 pl-10 focus:border-transparent focus:ring-2 focus:outline-none"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedTypes.length > 0 ? selectedTypes[0] : ""}
                  onChange={(e) =>
                    setSelectedTypes(e.target.value ? [e.target.value] : [])
                  }
                  className="focus:ring-primary rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:ring-2 focus:outline-none"
                >
                  <option value="">All Types</option>
                  <option value="document">Documents</option>
                  <option value="spreadsheet">Spreadsheets</option>
                  <option value="presentation">Presentations</option>
                  <option value="image">Images</option>
                  <option value="code">Code</option>
                </select>

                <select
                  value={selectedSources.length > 0 ? selectedSources[0] : ""}
                  onChange={(e) =>
                    setSelectedSources(e.target.value ? [e.target.value] : [])
                  }
                  className="focus:ring-primary rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:ring-2 focus:outline-none"
                >
                  <option value="">All Sources</option>
                  <option value="local">Local</option>
                  <option value="s3">S3</option>
                  <option value="google-drive">Google Drive</option>
                </select>

                <div className="flex overflow-hidden rounded-md border border-gray-300">
                  <button
                    className={`p-2 ${viewMode === "grid" ? "bg-gray-100" : "bg-white"}`}
                    onClick={() => setViewMode("grid")}
                    title="Grid View"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                      />
                    </svg>
                  </button>
                  <button
                    className={`p-2 ${viewMode === "list" ? "bg-gray-100" : "bg-white"}`}
                    onClick={() => setViewMode("list")}
                    title="List View"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6h16M4 10h16M4 14h16M4 18h16"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Document display */}
          <div className="rounded-lg bg-white shadow-sm">
            {filteredDocuments.length > 0 ? (
              viewMode === "grid" ? (
                <div className="grid grid-cols-1 gap-4 p-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                  {filteredDocuments.map((doc) => (
                    <div
                      key={doc.id}
                      className="rounded-lg border p-4 transition-shadow hover:shadow-md"
                    >
                      <div className="mb-2 flex items-center">
                        {getFileIcon(doc.type)}
                        <span className="ml-2 truncate font-medium text-gray-900">
                          {doc.name}
                        </span>
                      </div>
                      <div className="mb-1 text-xs text-gray-500">
                        Size: {doc.size}
                      </div>
                      <div className="mb-1 text-xs text-gray-500">
                        Modified: {doc.lastModified}
                      </div>
                      <div className="mb-2 text-xs text-gray-500">
                        Added by: {doc.addedBy}
                      </div>
                      <div className="flex items-center justify-between">
                        {getSourceBadge(doc.source)}
                        {getStatusBadge(doc.status)}
                      </div>
                      <div className="mt-3 flex justify-end space-x-2">
                        <button
                          className="text-gray-500 hover:text-gray-700"
                          title="View"
                        >
                          <Eye size={16} />
                        </button>
                        <button
                          className="text-gray-500 hover:text-gray-700"
                          title="Download"
                        >
                          <Download size={16} />
                        </button>
                        <button
                          className="text-red-500 hover:text-red-700"
                          title="Delete"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Name
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Type
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Size
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Modified
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Source
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Added By
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Status
                        </th>
                        <th
                          scope="col"
                          className="px-6 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase"
                        >
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {filteredDocuments.map((doc) => (
                        <tr key={doc.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              {getFileIcon(doc.type)}
                              <span className="ml-2 text-sm font-medium text-gray-900">
                                {doc.name}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-500">
                              {doc.type.toUpperCase()}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-500">
                              {doc.size}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-500">
                              {doc.lastModified}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getSourceBadge(doc.source)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className="text-sm text-gray-500">
                              {doc.addedBy}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {getStatusBadge(doc.status)}
                          </td>
                          <td className="px-6 py-4 text-right text-sm font-medium whitespace-nowrap">
                            <div className="flex justify-end space-x-2">
                              <button
                                className="text-gray-500 hover:text-gray-700"
                                title="View"
                              >
                                <Eye size={16} />
                              </button>
                              <button
                                className="text-gray-500 hover:text-gray-700"
                                title="Download"
                              >
                                <Download size={16} />
                              </button>
                              <button
                                className="text-red-500 hover:text-red-700"
                                title="Delete"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )
            ) : (
              <div className="p-8 text-center">
                <p className="text-gray-500">
                  No documents found matching your criteria.
                </p>
              </div>
            )}
          </div>
        </div>
      </main>

      {/* Upload modal */}
      {showUploader && (
        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black p-4">
          <div className="w-full max-w-2xl rounded-lg bg-white shadow-xl">
            <div className="flex items-center justify-between border-b p-4">
              <h2 className="text-lg font-semibold text-gray-900">
                Upload Documents
              </h2>
              <button
                onClick={() => setShowUploader(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X size={20} />
              </button>
            </div>
            <div className="p-6">
              <FileUploader onUpload={handleUpload} />
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default KnowledgeBase

interface FileUploaderProps {
  onUpload: (files: File[]) => void
}

const FileUploader: React.FC<FileUploaderProps> = ({ onUpload }) => {
  const [dragActive, setDragActive] = useState(false)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])

  const handleDrag = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)

    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      const newFiles = Array.from(e.dataTransfer.files)
      setSelectedFiles((prev) => [...prev, ...newFiles])
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles = Array.from(e.target.files)
      setSelectedFiles((prev) => [...prev, ...newFiles])
    }
  }

  const removeFile = (index: number) => {
    setSelectedFiles((prev) => prev.filter((_, i) => i !== index))
  }

  const handleUpload = () => {
    if (selectedFiles.length > 0) {
      onUpload(selectedFiles)
      setSelectedFiles([])
    }
  }

  return (
    <div className="w-full">
      <div
        className={`rounded-lg border-2 border-dashed p-6 text-center ${
          dragActive ? "border-primary bg-primary/10" : "border-gray-300"
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <Upload className="mx-auto mb-3 h-10 w-10 text-gray-400" />
        <p className="mb-1 text-sm text-gray-600">
          <span className="font-medium">Click to upload</span> or drag and drop
        </p>
        <p className="text-xs text-gray-500">
          PDF, DOC, XLS, CSV, TXT, JPG, PNG (max 20MB per file)
        </p>
        <input
          type="file"
          multiple
          onChange={handleFileChange}
          className="hidden"
          id="file-upload"
        />
        <label
          htmlFor="file-upload"
          className="mt-4 inline-block cursor-pointer rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50"
        >
          Select Files
        </label>
      </div>

      {selectedFiles.length > 0 && (
        <div className="mt-4">
          <div className="mb-2 flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-900">
              Selected Files ({selectedFiles.length})
            </h4>
            <button
              onClick={handleUpload}
              className="bg-primary hover:bg-primary/90 rounded px-3 py-1.5 text-xs font-medium text-white"
            >
              Upload All
            </button>
          </div>
          <div className="max-h-60 space-y-2 overflow-y-auto">
            {selectedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between rounded bg-gray-50 p-2"
              >
                <div className="flex items-center">
                  <div className="max-w-[250px] truncate text-sm text-gray-900">
                    {file.name}
                  </div>
                  <div className="ml-2 text-xs text-gray-500">
                    ({(file.size / 1024).toFixed(1)} KB)
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={16} />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
