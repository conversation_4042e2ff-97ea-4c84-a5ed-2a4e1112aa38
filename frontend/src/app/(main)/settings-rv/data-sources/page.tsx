"use client"

import React from "react"
import {
  AlertCircle,
  CheckCircle2,
  FileText,
  Plus,
  RefreshCw,
  <PERSON><PERSON>s,
  Trash2,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for connected data sources
const connectedSources = [
  {
    id: "epic-ehr",
    name: "Epic EHR",
    type: "Electronic Health Record",
    status: "Connected",
    lastSync: "Today at 06:30 AM",
    nextSync: "Today at 06:30 PM",
    recordsCount: 1250000,
    syncFrequency: "Every 12 hours",
    healthStatus: "Healthy",
  },
  {
    id: "cerner-ehr",
    name: "<PERSON><PERSON> EHR",
    type: "Electronic Health Record",
    status: "Connected",
    lastSync: "Today at 04:15 AM",
    nextSync: "Today at 04:15 PM",
    recordsCount: 980000,
    syncFrequency: "Every 12 hours",
    healthStatus: "Healthy",
  },
  {
    id: "allscripts",
    name: "Allscripts",
    type: "Practice Management",
    status: "Connected",
    lastSync: "Yesterday at 11:45 PM",
    nextSync: "Today at 11:45 PM",
    recordsCount: 450000,
    syncFrequency: "Daily",
    healthStatus: "Warning",
  },
  {
    id: "nextgen",
    name: "NextGen Healthcare",
    type: "Practice Management",
    status: "Error",
    lastSync: "3 days ago",
    nextSync: "Manual sync required",
    recordsCount: 320000,
    syncFrequency: "Daily",
    healthStatus: "Error",
  },
  {
    id: "labcorp",
    name: "LabCorp",
    type: "Laboratory Information System",
    status: "Connected",
    lastSync: "Today at 02:00 AM",
    nextSync: "Tomorrow at 02:00 AM",
    recordsCount: 780000,
    syncFrequency: "Daily",
    healthStatus: "Healthy",
  },
]

// Mock data for available data sources
const availableSources = [
  {
    id: "athenahealth",
    name: "athenahealth",
    type: "Electronic Health Record",
    description:
      "Connect to athenahealth EHR system to import patient records, appointments, and billing data.",
  },
  {
    id: "quest-diagnostics",
    name: "Quest Diagnostics",
    type: "Laboratory Information System",
    description:
      "Import lab results and diagnostic data from Quest Diagnostics.",
  },
  {
    id: "drchrono",
    name: "DrChrono",
    type: "Practice Management",
    description:
      "Connect to DrChrono for practice management data including scheduling and billing.",
  },
  {
    id: "eclinicalworks",
    name: "eClinicalWorks",
    type: "Electronic Health Record",
    description:
      "Import patient records, clinical documentation, and practice data from eClinicalWorks.",
  },
]

const DataSources = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Settings", href: "/settings-rv" },
        { label: "Data Sources" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Data Sources</CardTitle>
      <CardDescription>Connect and manage your data sources</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh Status
        </Button>
      </div>
      <Button size="sm">
        <Plus className="mr-2 h-4 w-4" />
        Add Data Source
      </Button>
    </div>

    <Tabs defaultValue="connected" className="mt-6">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="connected">Connected Sources</TabsTrigger>
        <TabsTrigger value="available">Available Sources</TabsTrigger>
      </TabsList>
      <TabsContent value="connected" className="mt-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {connectedSources.map((source) => (
            <Card key={source.id}>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{source.name}</CardTitle>
                  <span
                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs ${
                      source.healthStatus === "Healthy"
                        ? "bg-green-100 text-green-700"
                        : source.healthStatus === "Warning"
                          ? "bg-amber-100 text-amber-700"
                          : "bg-red-100 text-red-700"
                    }`}
                  >
                    {source.healthStatus}
                  </span>
                </div>
                <CardDescription>{source.type}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Status:</span>
                    <span
                      className={`flex items-center ${
                        source.status === "Connected"
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {source.status === "Connected" ? (
                        <CheckCircle2 className="mr-1 h-3 w-3" />
                      ) : (
                        <AlertCircle className="mr-1 h-3 w-3" />
                      )}
                      {source.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Last Sync:</span>
                    <span>{source.lastSync}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Next Sync:</span>
                    <span>{source.nextSync}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Records:</span>
                    <span>{source.recordsCount.toLocaleString()}</span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Frequency:</span>
                    <span>{source.syncFrequency}</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between pt-0">
                <Button variant="outline" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  Configure
                </Button>
                <Button variant="outline" size="sm">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Sync Now
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>
      <TabsContent value="available" className="mt-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {availableSources.map((source) => (
            <Card key={source.id}>
              <CardHeader>
                <CardTitle className="text-lg">{source.name}</CardTitle>
                <CardDescription>{source.type}</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground text-sm">
                  {source.description}
                </p>
              </CardContent>
              <CardFooter>
                <Button className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  Connect
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </TabsContent>
    </Tabs>

    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Data Source Logs</CardTitle>
        <CardDescription>
          Recent activity and sync logs for your data sources
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b">
                <th className="p-2 text-left font-medium">Timestamp</th>
                <th className="p-2 text-left font-medium">Data Source</th>
                <th className="p-2 text-left font-medium">Event</th>
                <th className="p-2 text-left font-medium">Status</th>
                <th className="p-2 text-left font-medium">Details</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="p-2 text-sm">2024-05-15 06:30:12</td>
                <td className="p-2 text-sm">Epic EHR</td>
                <td className="p-2 text-sm">Scheduled Sync</td>
                <td className="p-2 text-sm">
                  <span className="inline-flex items-center text-green-600">
                    <CheckCircle2 className="mr-1 h-3 w-3" />
                    Success
                  </span>
                </td>
                <td className="p-2 text-sm">Synced 12,450 new records</td>
              </tr>
              <tr className="border-b">
                <td className="p-2 text-sm">2024-05-15 04:15:08</td>
                <td className="p-2 text-sm">Cerner EHR</td>
                <td className="p-2 text-sm">Scheduled Sync</td>
                <td className="p-2 text-sm">
                  <span className="inline-flex items-center text-green-600">
                    <CheckCircle2 className="mr-1 h-3 w-3" />
                    Success
                  </span>
                </td>
                <td className="p-2 text-sm">Synced 8,320 new records</td>
              </tr>
              <tr className="border-b">
                <td className="p-2 text-sm">2024-05-14 23:45:22</td>
                <td className="p-2 text-sm">Allscripts</td>
                <td className="p-2 text-sm">Scheduled Sync</td>
                <td className="p-2 text-sm">
                  <span className="inline-flex items-center text-amber-600">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    Warning
                  </span>
                </td>
                <td className="p-2 text-sm">
                  Synced 3,210 records with 42 validation warnings
                </td>
              </tr>
              <tr className="border-b">
                <td className="p-2 text-sm">2024-05-14 02:00:05</td>
                <td className="p-2 text-sm">LabCorp</td>
                <td className="p-2 text-sm">Scheduled Sync</td>
                <td className="p-2 text-sm">
                  <span className="inline-flex items-center text-green-600">
                    <CheckCircle2 className="mr-1 h-3 w-3" />
                    Success
                  </span>
                </td>
                <td className="p-2 text-sm">Synced 5,680 new records</td>
              </tr>
              <tr className="border-b">
                <td className="p-2 text-sm">2024-05-12 11:45:30</td>
                <td className="p-2 text-sm">NextGen Healthcare</td>
                <td className="p-2 text-sm">Scheduled Sync</td>
                <td className="p-2 text-sm">
                  <span className="inline-flex items-center text-red-600">
                    <AlertCircle className="mr-1 h-3 w-3" />
                    Failed
                  </span>
                </td>
                <td className="p-2 text-sm">
                  API authentication error: Invalid credentials
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" size="sm">
          <FileText className="mr-2 h-4 w-4" />
          View All Logs
        </Button>
        <Button variant="outline" size="sm">
          <Trash2 className="mr-2 h-4 w-4" />
          Clear Logs
        </Button>
      </CardFooter>
    </Card>

    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Data Source Health</CardTitle>
        <CardDescription>
          Overall health and performance metrics for your data sources
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-6 md:grid-cols-2">
          <div>
            <h3 className="mb-2 text-sm font-medium">
              Sync Success Rate (Last 30 Days)
            </h3>
            <div className="space-y-4">
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">Epic EHR</span>
                  <span className="text-sm font-medium">98.3%</span>
                </div>
                <Progress value={98.3} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">Cerner EHR</span>
                  <span className="text-sm font-medium">99.1%</span>
                </div>
                <Progress value={99.1} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">Allscripts</span>
                  <span className="text-sm font-medium">92.5%</span>
                </div>
                <Progress value={92.5} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">NextGen Healthcare</span>
                  <span className="text-sm font-medium">78.2%</span>
                </div>
                <Progress value={78.2} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">LabCorp</span>
                  <span className="text-sm font-medium">97.8%</span>
                </div>
                <Progress value={97.8} className="h-2" />
              </div>
            </div>
          </div>
          <div>
            <h3 className="mb-2 text-sm font-medium">Data Quality Score</h3>
            <div className="space-y-4">
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">Epic EHR</span>
                  <span className="text-sm font-medium">96.2%</span>
                </div>
                <Progress value={96.2} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">Cerner EHR</span>
                  <span className="text-sm font-medium">94.8%</span>
                </div>
                <Progress value={94.8} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">Allscripts</span>
                  <span className="text-sm font-medium">88.3%</span>
                </div>
                <Progress value={88.3} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">NextGen Healthcare</span>
                  <span className="text-sm font-medium">82.1%</span>
                </div>
                <Progress value={82.1} className="h-2" />
              </div>
              <div>
                <div className="mb-1 flex items-center justify-between">
                  <span className="text-sm">LabCorp</span>
                  <span className="text-sm font-medium">95.5%</span>
                </div>
                <Progress value={95.5} className="h-2" />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </>
)

export default DataSources
