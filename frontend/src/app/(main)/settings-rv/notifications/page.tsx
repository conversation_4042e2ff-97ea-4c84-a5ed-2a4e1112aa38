"use client"

import React from "react"
import {
  Bell,
  BellOff,
  Mail,
  MessageSquare,
  Save,
  Smartphone,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for notification categories
const notificationCategories = [
  {
    id: "system",
    title: "System Notifications",
    description: "Updates about the OneFlow platform",
    notifications: [
      {
        id: "system-updates",
        title: "System Updates",
        description: "Notifications about system updates and maintenance",
        email: true,
        push: true,
        inApp: true,
      },
      {
        id: "security-alerts",
        title: "Security Alerts",
        description: "Important security notifications and alerts",
        email: true,
        push: true,
        inApp: true,
      },
      {
        id: "feature-announcements",
        title: "Feature Announcements",
        description: "Announcements about new features and improvements",
        email: true,
        push: false,
        inApp: true,
      },
    ],
  },
  {
    id: "data",
    title: "Data Notifications",
    description: "Notifications related to your data sources",
    notifications: [
      {
        id: "etl-status",
        title: "ETL Status",
        description: "Updates about ETL job status and failures",
        email: true,
        push: true,
        inApp: true,
      },
      {
        id: "data-quality",
        title: "Data Quality Alerts",
        description: "Alerts about data quality issues",
        email: true,
        push: true,
        inApp: true,
      },
      {
        id: "data-sync",
        title: "Data Sync Completion",
        description: "Notifications when data syncs are completed",
        email: false,
        push: false,
        inApp: true,
      },
    ],
  },
  {
    id: "users",
    title: "User Notifications",
    description: "Notifications about user activity",
    notifications: [
      {
        id: "new-users",
        title: "New User Registration",
        description: "Notifications when new users register",
        email: true,
        push: false,
        inApp: true,
      },
      {
        id: "role-changes",
        title: "Role Changes",
        description: "Alerts about user role changes",
        email: true,
        push: false,
        inApp: true,
      },
      {
        id: "login-attempts",
        title: "Failed Login Attempts",
        description: "Notifications about failed login attempts",
        email: true,
        push: true,
        inApp: true,
      },
    ],
  },
]

// Mock data for notification history
const notificationHistory = [
  {
    id: 1,
    title: "System Update Scheduled",
    message:
      "A system update is scheduled for May 20, 2024 at 2:00 AM EST. The system will be unavailable for approximately 30 minutes.",
    type: "System",
    date: "May 15, 2024",
    time: "10:23 AM",
    read: true,
  },
  {
    id: 2,
    title: "Data Quality Alert",
    message:
      "High severity data quality issue detected in Epic EHR data source. 128 records affected with invalid date format.",
    type: "Data",
    date: "May 15, 2024",
    time: "08:45 AM",
    read: true,
  },
  {
    id: 3,
    title: "New User Registration",
    message:
      "Jennifer Garcia has registered as a new user. Role: Nurse, Department: Pediatrics.",
    type: "User",
    date: "May 15, 2024",
    time: "08:30 AM",
    read: false,
  },
  {
    id: 4,
    title: "ETL Job Failed",
    message:
      "Allscripts Daily Import ETL job failed due to connection timeout. Manual restart required.",
    type: "Data",
    date: "May 15, 2024",
    time: "02:15 AM",
    read: false,
  },
  {
    id: 5,
    title: "Security Alert",
    message:
      "Multiple failed login attempts detected <NAME_EMAIL> from an unrecognized IP address.",
    type: "Security",
    date: "May 14, 2024",
    time: "03:45 PM",
    read: true,
  },
]

const Notifications = () => (
  <>
    <Breadcrumb
      links={[
        { label: "Settings", href: "/settings-rv" },
        { label: "Notifications" },
      ]}
    />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Notifications</CardTitle>
      <CardDescription>Manage your notification preferences</CardDescription>
    </div>

    <UnderConstructionAlert />

    <Tabs defaultValue="preferences" className="mt-6">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="preferences">Notification Preferences</TabsTrigger>
        <TabsTrigger value="history">Notification History</TabsTrigger>
      </TabsList>
      <TabsContent value="preferences" className="mt-4">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="text-primary h-5 w-5" />
            <h2 className="text-lg font-medium">Notification Channels</h2>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Mail className="text-muted-foreground h-4 w-4" />
              <span className="text-sm">Email</span>
            </div>
            <div className="flex items-center gap-2">
              <Smartphone className="text-muted-foreground h-4 w-4" />
              <span className="text-sm">Push</span>
            </div>
            <div className="flex items-center gap-2">
              <MessageSquare className="text-muted-foreground h-4 w-4" />
              <span className="text-sm">In-App</span>
            </div>
          </div>
        </div>

        <div className="space-y-6">
          {notificationCategories.map((category) => (
            <Card key={category.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>{category.title}</CardTitle>
                    <CardDescription>{category.description}</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <BellOff className="mr-2 h-4 w-4" />
                      Mute All
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {category.notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0"
                    >
                      <div>
                        <h3 className="font-medium">{notification.title}</h3>
                        <p className="text-muted-foreground text-sm">
                          {notification.description}
                        </p>
                      </div>
                      <div className="flex items-center gap-8">
                        <div className="flex items-center gap-2">
                          <Switch
                            id={`${notification.id}-email`}
                            checked={notification.email}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            id={`${notification.id}-push`}
                            checked={notification.push}
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <Switch
                            id={`${notification.id}-inapp`}
                            checked={notification.inApp}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-6 flex justify-end">
          <Button>
            <Save className="mr-2 h-4 w-4" />
            Save Preferences
          </Button>
        </div>
      </TabsContent>
      <TabsContent value="history" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle>Notification History</CardTitle>
            <CardDescription>
              Recent notifications {`you've`} received
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {notificationHistory.map((notification) => (
                <div
                  key={notification.id}
                  className={`border-l-4 ${notification.read ? "border-gray-200" : "border-primary"} py-2 pl-4`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <h3
                        className={`font-medium ${notification.read ? "" : "text-primary"}`}
                      >
                        {notification.title}
                      </h3>
                      <span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs">
                        {notification.type}
                      </span>
                    </div>
                    <span className="text-muted-foreground text-xs">
                      {notification.date} at {notification.time}
                    </span>
                  </div>
                  <p className="mt-1 text-sm">{notification.message}</p>
                </div>
              ))}
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button variant="outline" size="sm">
              Mark All as Read
            </Button>
            <Button variant="outline" size="sm">
              View All Notifications
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>

    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Delivery Settings</CardTitle>
        <CardDescription>
          Configure how and when you receive notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="mb-3 text-sm font-medium">Email Notifications</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="email-delivery">Email Delivery Method</Label>
                  <p className="text-muted-foreground text-sm">
                    Choose how you want to receive email notifications
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <select
                    id="email-delivery"
                    className="border-input bg-background focus-visible:ring-ring h-9 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none"
                  >
                    <option value="immediate">Immediate</option>
                    <option value="digest">Daily Digest</option>
                    <option value="weekly">Weekly Summary</option>
                  </select>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="email-format">Email Format</Label>
                  <p className="text-muted-foreground text-sm">
                    Choose your preferred email format
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <select
                    id="email-format"
                    className="border-input bg-background focus-visible:ring-ring h-9 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none"
                  >
                    <option value="html">HTML</option>
                    <option value="text">Plain Text</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="mb-3 text-sm font-medium">Push Notifications</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="quiet-hours">Quiet Hours</Label>
                  <p className="text-muted-foreground text-sm">
                    {`Don't`} send push notifications during these hours
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <select
                      id="quiet-hours-start"
                      className="border-input bg-background focus-visible:ring-ring h-9 w-24 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none"
                    >
                      <option value="20">8:00 PM</option>
                      <option value="21">9:00 PM</option>
                      <option value="22">10:00 PM</option>
                      <option value="23">11:00 PM</option>
                    </select>
                    <span className="text-sm">to</span>
                    <select
                      id="quiet-hours-end"
                      className="border-input bg-background focus-visible:ring-ring h-9 w-24 rounded-md border px-3 py-1 text-sm shadow-sm transition-colors focus-visible:ring-1 focus-visible:outline-none"
                    >
                      <option value="6">6:00 AM</option>
                      <option value="7">7:00 AM</option>
                      <option value="8">8:00 AM</option>
                      <option value="9">9:00 AM</option>
                    </select>
                  </div>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="push-devices">Registered Devices</Label>
                  <p className="text-muted-foreground text-sm">
                    Devices that will receive push notifications
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Manage Devices
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button>
          <Save className="mr-2 h-4 w-4" />
          Save Delivery Settings
        </Button>
      </CardFooter>
    </Card>
  </>
)

export default Notifications
