"use client"

import React from "react"
import {
  Bell,
  Database,
  Globe,
  Lock,
  Settings as SettingsIcon,
  Shield,
  User,
  Users,
} from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

// Mock data for settings categories
const settingsCategories = [
  {
    id: "users",
    title: "Users & Roles",
    description: "Manage users, roles, and permissions",
    icon: Users,
    href: "/settings-rv/users",
    updates: 2,
  },
  {
    id: "data-sources",
    title: "Data Sources",
    description: "Connect and manage your data sources",
    icon: Database,
    href: "/settings-rv/data-sources",
    updates: 1,
  },
  {
    id: "notifications",
    title: "Notifications",
    description: "Configure notification preferences",
    icon: Bell,
    href: "/settings-rv/notifications",
    updates: 0,
  },
  {
    id: "security",
    title: "Security",
    description: "Security settings and access controls",
    icon: Shield,
    href: "/settings-rv/security",
    updates: 0,
  },
  {
    id: "account",
    title: "Account",
    description: "Manage your account settings",
    icon: User,
    href: "/settings-rv/account",
    updates: 0,
  },
  {
    id: "organization",
    title: "Organization",
    description: "Organization profile and settings",
    icon: Globe,
    href: "/settings-rv/organization",
    updates: 0,
  },
]

// Mock data for recent activity
const recentActivity = [
  {
    id: 1,
    user: "Dr. Sarah Johnson",
    action: "Updated data source connection",
    target: "Epic EHR",
    timestamp: "Today at 10:23 AM",
  },
  {
    id: 2,
    user: "Dr. Sarah Johnson",
    action: "Added new user",
    target: "Jennifer Garcia",
    timestamp: "Today at 09:45 AM",
  },
  {
    id: 3,
    user: "Lisa Patel",
    action: "Modified role permissions",
    target: "Analyst Role",
    timestamp: "Yesterday at 04:30 PM",
  },
  {
    id: 4,
    user: "System",
    action: "Automatic backup completed",
    target: "All data sources",
    timestamp: "Yesterday at 02:00 AM",
  },
  {
    id: 5,
    user: "James Wilson",
    action: "Updated notification settings",
    target: "Email notifications",
    timestamp: "May 14, 2024 at 11:15 AM",
  },
]

const Settings = () => (
  <>
    <Breadcrumb links={[{ label: "Settings" }]} />

    <div className="flex flex-col gap-1.5">
      <CardTitle>Settings</CardTitle>
      <CardDescription>Settings for OneFlow platform</CardDescription>
    </div>

    <UnderConstructionAlert />

    <div className="mt-6 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {settingsCategories.map((category) => (
        <Card key={category.id} className="relative">
          {category.updates > 0 && (
            <span className="bg-primary absolute top-3 right-3 flex h-5 w-5 items-center justify-center rounded-full text-xs text-white">
              {category.updates}
            </span>
          )}
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <category.icon className="text-primary h-5 w-5" />
              <CardTitle className="text-lg">{category.title}</CardTitle>
            </div>
            <CardDescription>{category.description}</CardDescription>
          </CardHeader>
          <CardFooter className="pt-2">
            <Button variant="outline" size="sm" asChild>
              <a href={category.href}>Manage</a>
            </Button>
          </CardFooter>
        </Card>
      ))}
    </div>

    <div className="mt-6 grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <SettingsIcon className="text-primary h-5 w-5" />
              <CardTitle className="text-lg">System Information</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-muted-foreground text-sm font-medium">
                  Version
                </h3>
                <p className="text-sm">OneFlow v2.4.0</p>
              </div>
              <div>
                <h3 className="text-muted-foreground text-sm font-medium">
                  Last Updated
                </h3>
                <p className="text-sm">May 15, 2024</p>
              </div>
              <div>
                <h3 className="text-muted-foreground text-sm font-medium">
                  Environment
                </h3>
                <p className="text-sm">Production</p>
              </div>
              <div>
                <h3 className="text-muted-foreground text-sm font-medium">
                  Database Status
                </h3>
                <p className="text-sm text-green-600">Connected</p>
              </div>
            </div>
            <div>
              <h3 className="text-muted-foreground text-sm font-medium">
                System Health
              </h3>
              <div className="mt-1 flex items-center gap-2">
                <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
                  All Systems Operational
                </span>
              </div>
            </div>
            <div>
              <h3 className="text-muted-foreground text-sm font-medium">
                License
              </h3>
              <div className="mt-1 flex items-center justify-between">
                <span className="text-sm">Enterprise License</span>
                <span className="text-sm">Expires: Dec 31, 2024</span>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm">
            Check for Updates
          </Button>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Lock className="text-primary h-5 w-5" />
              <CardTitle className="text-lg">Recent Activity</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div
                key={activity.id}
                className="border-b pb-3 last:border-0 last:pb-0"
              >
                <div className="flex justify-between">
                  <span className="font-medium">{activity.user}</span>
                  <span className="text-muted-foreground text-xs">
                    {activity.timestamp}
                  </span>
                </div>
                <p className="text-sm">
                  {activity.action}:{" "}
                  <span className="font-medium">{activity.target}</span>
                </p>
              </div>
            ))}
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline" size="sm">
            View All Activity
          </Button>
        </CardFooter>
      </Card>
    </div>
  </>
)

export default Settings
