"use client"

import React from "react"

import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

const Settings = () => (
  <>
    <Breadcrumb links={[{ label: "Settings" }]} />

    <UnderConstructionAlert />

    <main className="flex-grow overflow-auto">
      <div>
        <h1 className="text-primary border-primary mb-6 border-b pb-2 text-2xl font-semibold">
          Settings
        </h1>

        {/* Settings navigation */}
        <div className="mb-6 rounded-lg bg-white shadow-md">
          <div className="flex border-b border-gray-200">
            <button className="text-primary border-primary border-b-2 px-6 py-4 font-medium">
              Account
            </button>
            <button className="px-6 py-4 text-gray-500 hover:text-gray-700">
              Appearance
            </button>
            <button className="px-6 py-4 text-gray-500 hover:text-gray-700">
              Notifications
            </button>
            <button className="px-6 py-4 text-gray-500 hover:text-gray-700">
              Data & Privacy
            </button>
            <button className="px-6 py-4 text-gray-500 hover:text-gray-700">
              Usage
            </button>
          </div>
        </div>

        {/* Account settings section */}
        <div className="mb-6 rounded-lg bg-white p-6 shadow-md">
          <div className="mb-6 flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Account Settings
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                Manage your account information and preferences.
              </p>
            </div>
          </div>

          <div className="mb-8 flex items-start border-b border-gray-200 pb-6">
            <div className="flex-grow">
              <div className="flex items-center">
                <h3 className="text-md font-medium text-gray-900">John Doe</h3>
                <span className="ml-2 rounded bg-gray-100 px-2 py-0.5 text-xs text-gray-600">
                  Financial Analyst
                </span>
              </div>
              <p className="mt-1 text-sm text-gray-600"><EMAIL></p>
              <div className="mt-3 flex space-x-3">
                <button className="rounded bg-gray-100 px-3 py-1.5 text-sm text-gray-600 transition-colors hover:bg-gray-200">
                  Edit Profile
                </button>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            {/* Personal Information */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-md mb-4 font-medium text-gray-900">
                Personal Information
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    First Name
                  </label>
                  <input
                    type="text"
                    defaultValue="John"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    Last Name
                  </label>
                  <input
                    type="text"
                    defaultValue="Doe"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    Email Address
                  </label>
                  <input
                    type="email"
                    defaultValue="<EMAIL>"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    Job Title
                  </label>
                  <input
                    type="text"
                    defaultValue="Financial Analyst"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Password */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-md mb-4 font-medium text-gray-900">
                Password
              </h3>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    Current Password
                  </label>
                  <input
                    type="password"
                    defaultValue="********"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
                <div></div>
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    New Password
                  </label>
                  <input
                    type="password"
                    placeholder="Enter new password"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
                <div>
                  <label className="mb-1 block text-sm text-gray-600">
                    Confirm New Password
                  </label>
                  <input
                    type="password"
                    placeholder="Confirm new password"
                    className="w-full rounded border border-gray-300 bg-white px-3 py-2 text-gray-700 focus:border-gray-400 focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Display Preferences - Keeping these based on user preferences in memory */}
            <div className="border-b border-gray-200 pb-6">
              <h3 className="text-md mb-4 font-medium text-gray-900">
                Display Preferences
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox text-primary h-4 w-4"
                      defaultChecked
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Color-code financial changes (green for positive, red for
                      negative)
                    </span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox text-primary h-4 w-4"
                      defaultChecked
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Use numbered points for lists in analysis reports
                    </span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox text-primary h-4 w-4"
                      defaultChecked
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Context-dependent coloring (e.g., costs going down is
                      positive/green)
                    </span>
                  </label>
                </div>
                <div>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="form-checkbox text-primary h-4 w-4"
                      defaultChecked
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      Show clear visual hierarchy in reporting
                    </span>
                  </label>
                </div>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end">
              <button className="bg-primary hover:bg-primary rounded px-6 py-2 text-sm text-white transition-colors">
                Save Changes
              </button>
            </div>
          </div>
        </div>

        {/* Danger Zone */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-md">
          <h2 className="mb-4 text-lg font-semibold text-gray-900">
            Data Management
          </h2>
          <p className="mb-4 text-sm text-gray-600">
            Export or delete your account data. Deletion is irreversible and
            will remove all your data from our systems.
          </p>
          <div className="flex space-x-4">
            <button className="rounded border border-red-300 bg-red-50 px-4 py-2 text-sm text-red-700 transition-colors hover:bg-red-100">
              Delete Account
            </button>
            <button className="rounded border border-gray-300 bg-gray-100 px-4 py-2 text-sm text-gray-700 transition-colors hover:bg-gray-200">
              Export All Data
            </button>
          </div>
        </div>
      </div>
    </main>
  </>
)

export default Settings
