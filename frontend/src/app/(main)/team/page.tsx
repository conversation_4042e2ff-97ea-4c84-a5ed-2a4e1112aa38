"use client"

import React from "react"

import UnderConstructionAlert from "@/components/UnderConstructionAlert"
import { Breadcrumb } from "@/contexts/breadcrumb"

const teamMembers = [
  {
    id: 1,
    name: "<PERSON>",
    title: "Chief Financial Officer",
    department: "Executive",
    email: "emily.<PERSON><PERSON><PERSON><PERSON>@leviathan.com",
    avatar: "👩‍💼",
    recentAnalyses: 3,
  },
  {
    id: 2,
    name: "<PERSON>",
    title: "VP of Financial Planning",
    department: "Finance",
    email: "<EMAIL>",
    avatar: "👨‍💼",
    recentAnalyses: 5,
  },
  {
    id: 3,
    name: "<PERSON>",
    title: "Financial Analyst",
    department: "Finance",
    email: "<EMAIL>",
    avatar: "👩‍💻",
    recentAnalyses: 8,
  },
  {
    id: 4,
    name: "<PERSON>",
    title: "Data Scientist",
    department: "Analytics",
    email: "<EMAIL>",
    avatar: "👨‍💻",
    recentAnalyses: 6,
  },
  {
    id: 5,
    name: "<PERSON>",
    title: "Business Intelligence Manager",
    department: "Analytics",
    email: "<EMAIL>",
    avatar: "👩‍💼",
    recentAnalyses: 4,
  },
  {
    id: 6,
    name: "<PERSON>",
    title: "Controller",
    department: "Finance",
    email: "<EMAIL>",
    avatar: "👨‍💼",
    recentAnalyses: 2,
  },
]

const Team = () => {
  return (
    <>
      <Breadcrumb links={[{ label: "Team" }]} />

      <UnderConstructionAlert />

      <main className="flex-grow overflow-auto">
        <div>
          <h1 className="text-primary border-primary mb-6 border-b pb-2 text-2xl font-semibold">
            Team
          </h1>

          <div className="mb-6 rounded-lg bg-white p-6 shadow-md">
            <div className="mb-6 flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">
                  Team Members
                </h2>
                <p className="mt-1 text-sm text-gray-600">
                  Financial team members with access to the OneFlow platform.
                </p>
              </div>
              <div className="flex space-x-2">
                <button className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50">
                  Invite Member
                </button>
                <button className="rounded border border-gray-300 px-3 py-1.5 text-xs font-medium text-gray-700 hover:bg-gray-50">
                  Manage Permissions
                </button>
              </div>
            </div>

            {/* Team members table */}
            <div className="overflow-hidden rounded border border-gray-200">
              <table className="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr className="bg-gray-50">
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Team Member
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Title
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Department
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Recent Activity
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium tracking-wider text-gray-500 uppercase">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white">
                  {teamMembers.map((member) => (
                    <tr key={member.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">
                        <div className="flex items-center">
                          <div className="text-sm font-medium text-gray-900">
                            {member.name}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-sm text-gray-600">
                          {member.title}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-sm text-gray-600">
                          {member.department}
                        </div>
                      </td>
                      <td className="px-4 py-3">
                        <div className="text-sm text-gray-600">
                          {member.recentAnalyses} recent analyses
                        </div>
                      </td>
                      <td className="px-4 py-3 text-right text-sm font-medium">
                        <button className="mr-3 text-gray-600 hover:text-gray-900">
                          View
                        </button>
                        <button className="text-gray-600 hover:text-gray-900">
                          Edit
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}

export default Team
